import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../design_system/app_theme.dart';
import '../../design_system/components/enhanced_backgrounds.dart';
import '../../models/job_model.dart';
import '../../navigation/app_router.dart';
import '../../providers/app_state_provider.dart';
import '../../widgets/notification_badge.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _sparkController;

  @override
  void initState() {
    super.initState();
    _sparkController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateProvider>().refreshJobs();
    });
  }

  @override
  void dispose() {
    _sparkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        backgroundColor: AppTheme.offWhite,
        appBar: EnhancedBackgrounds.enhancedAppBar(
          title: 'Journeyman Jobs',
          actions: <Widget>[
            NotificationBadge(
              iconColor: AppTheme.white,
              showPopupOnTap: false,
              onTap: () {
                context.push(AppRouter.notifications);
              },
            ),
          ],
        ),
        body: EnhancedBackgrounds.circuitPatternBackground(
          opacity: 0.03,
          child: RefreshIndicator(
            onRefresh: () => context.read<AppStateProvider>().refreshJobs(),
            color: AppTheme.accentCopper,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Welcome Section with Enhanced Card
                  EnhancedBackgrounds.enhancedCardBackground(
                    margin: const EdgeInsets.only(bottom: AppTheme.spacingLg),
                    child: Consumer<AppStateProvider>(
                      builder: (
                        BuildContext context,
                        AppStateProvider appStateProvider,
                        Widget? child,
                      ) {
                        if (!appStateProvider.isAuthenticated) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Row(
                                children: <Widget>[
                                  Container(
                                    width: 60,
                                    height: 60,
                                    decoration: const BoxDecoration(
                                      gradient: AppTheme.buttonGradient,
                                      shape: BoxShape.circle,
                                      boxShadow: <BoxShadow>[AppTheme.shadowMd],
                                    ),
                                    child: const Icon(
                                      Icons.electrical_services,
                                      size: 32,
                                      color: AppTheme.white,
                                    ),
                                  ),
                                  const SizedBox(width: AppTheme.spacingMd),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text(
                                          'Welcome back!',
                                          style:
                                              AppTheme.headlineMedium.copyWith(
                                            color: AppTheme.primaryNavy,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(
                                          height: AppTheme.spacingXs,
                                        ),
                                        Text(
                                          'Guest User',
                                          style: AppTheme.bodyLarge.copyWith(
                                            color: AppTheme.textSecondary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        }

                        final String displayName =
                            appStateProvider.userProfile?.displayName ??
                                appStateProvider.user?.displayName ??
                                'User';
                        final String? photoUrl =
                            appStateProvider.user?.photoURL;
                        final String userInitial = displayName.isNotEmpty
                            ? displayName[0].toUpperCase()
                            : 'U';

                        return Row(
                          children: <Widget>[
                            Container(
                              width: 60,
                              height: 60,
                              decoration: const BoxDecoration(
                                gradient: AppTheme.splashGradient,
                                shape: BoxShape.circle,
                                boxShadow: <BoxShadow>[AppTheme.shadowMd],
                              ),
                              child: photoUrl != null
                                  ? ClipOval(
                                      child: Image.network(
                                        photoUrl,
                                        fit: BoxFit.cover,
                                        errorBuilder: (
                                          BuildContext context,
                                          Object error,
                                          StackTrace? stackTrace,
                                        ) =>
                                            Center(
                                          child: Text(
                                            userInitial,
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 24,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  : Center(
                                      child: Text(
                                        userInitial,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                            ),
                            const SizedBox(width: AppTheme.spacingMd),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(
                                    'Welcome back',
                                    style: AppTheme.headlineMedium.copyWith(
                                      color: AppTheme.primaryNavy,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: AppTheme.spacingXs),
                                  Text(
                                    displayName,
                                    style: AppTheme.bodyLarge.copyWith(
                                      color: AppTheme.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),

                  // Quick Stats Section
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: _buildElectricalStatCard(
                          icon: Icons.work_outline,
                          label: 'Active Jobs',
                          value: context
                              .watch<AppStateProvider>()
                              .jobs
                              .length
                              .toString(),
                          color: AppTheme.successGreen,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingSm),
                      Expanded(
                        child: _buildElectricalStatCard(
                          icon: Icons.location_city,
                          label: 'IBEW Locals',
                          value: '797+',
                          color: AppTheme.accentCopper,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.spacingLg),

                  // Quick Actions Grid with Electrical Theme
                  Text(
                    'Quick Actions',
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMd),
                  _buildQuickActionsGrid(context),
                  const SizedBox(height: AppTheme.spacingLg),

                  // Recent Jobs Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Text(
                        'Recent Jobs',
                        style: AppTheme.headlineSmall.copyWith(
                          color: AppTheme.primaryNavy,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          context.push(AppRouter.jobs);
                        },
                        child: Text(
                          'View All',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.accentCopper,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.spacingSm),
                  _buildRecentJobsList(context),
                ],
              ),
            ),
          ),
        ),
      );

  Widget _buildElectricalStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) =>
      EnhancedBackgrounds.enhancedCardBackground(
        showCircuitPattern: false,
        child: Row(
          children: <Widget>[
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: AppTheme.spacingSm),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    value,
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    label,
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );

  Widget _buildQuickActionsGrid(BuildContext context) {
    final actions = <Map<String, Object>>[
      <String, Object>{
        'icon': Icons.search,
        'label': 'Find Jobs',
        'gradient': <Color>[AppTheme.accentCopper, AppTheme.secondaryCopper],
        'onTap': () => context.push(AppRouter.jobs),
      },
      <String, Object>{
        'icon': Icons.location_city,
        'label': 'IBEW Locals',
        'gradient': <Color>[AppTheme.primaryNavy, AppTheme.secondaryNavy],
        'onTap': () => context.push(AppRouter.locals),
      },
      <String, Object>{
        'icon': Icons.thunderstorm,
        'label': 'Storm Work',
        'gradient': <Color>[AppTheme.warningYellow, AppTheme.accentCopper],
        'onTap': () => context.push(AppRouter.storm),
      },
      <String, Object>{
        'icon': Icons.calculate,
        'label': 'Calculators',
        'gradient': <Color>[AppTheme.successGreen, const Color(0xFF2D7A3D)],
        'onTap': () => context.push(AppRouter.electricalCalculators),
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppTheme.spacingSm,
        mainAxisSpacing: AppTheme.spacingSm,
        childAspectRatio: 1.5,
      ),
      itemCount: actions.length,
      itemBuilder: (BuildContext context, int index) {
        final action = actions[index];
        return _buildQuickActionCard(
          icon: action['icon']! as IconData,
          label: action['label']! as String,
          gradient: action['gradient']! as List<Color>,
          onTap: action['onTap']! as VoidCallback,
        );
      },
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String label,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) =>
      Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.radiusMd),
          splashColor: AppTheme.white.withValues(alpha: 0.2),
          highlightColor: AppTheme.white.withValues(alpha: 0.1),
          child: DecoratedBox(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: gradient,
                stops: const <double>[0, 1],
              ),
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              border: Border.all(
                color: AppTheme.white.withValues(alpha: 0.2),
              ),
              boxShadow: <BoxShadow>[
                AppTheme.shadowMd,
                BoxShadow(
                  color: gradient.first.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Stack(
              children: <Widget>[
                // Circuit pattern overlay
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                    child: CustomPaint(
                      painter: CircuitPatternPainter(
                        color: AppTheme.white,
                        opacity: 0.08,
                      ),
                    ),
                  ),
                ),
                // Subtle inner glow
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: <Color>[
                          AppTheme.white.withValues(alpha: 0.1),
                          Colors.transparent,
                          AppTheme.primaryNavy.withValues(alpha: 0.1),
                        ],
                        stops: const <double>[0, 0.5, 1],
                      ),
                    ),
                  ),
                ),
                // Content
                Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingMd),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        padding: const EdgeInsets.all(AppTheme.spacingSm),
                        decoration: BoxDecoration(
                          color: AppTheme.white.withValues(alpha: 0.15),
                          borderRadius:
                              BorderRadius.circular(AppTheme.radiusSm),
                          border: Border.all(
                            color: AppTheme.white.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Icon(
                          icon,
                          size: AppTheme.iconLg,
                          color: AppTheme.white,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingSm),
                      Text(
                        label,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.white,
                          fontWeight: FontWeight.w600,
                          shadows: <Shadow>[
                            Shadow(
                              color:
                                  AppTheme.primaryNavy.withValues(alpha: 0.5),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  Widget _buildRecentJobsList(BuildContext context) =>
      Consumer<AppStateProvider>(
        builder: (
          BuildContext context,
          AppStateProvider appStateProvider,
          Widget? child,
        ) {
          final List<Job> jobs = appStateProvider.jobs;

          if (appStateProvider.isLoadingJobs) {
            return Center(
              child: EnhancedBackgrounds.sparkEffectBackground(
                child: Container(
                  height: 150,
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.accentCopper,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingMd),
                      Text(
                        'Energizing job listings...',
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }

          if (jobs.isEmpty) {
            return EnhancedBackgrounds.enhancedCardBackground(
              child: Container(
                height: 150,
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Icon(
                      Icons.electrical_services_outlined,
                      size: 48,
                      color: AppTheme.textSecondary.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: AppTheme.spacingSm),
                    Text(
                      'No jobs available',
                      style: AppTheme.bodyLarge.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingXs),
                    Text(
                      'Check back soon for new opportunities',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textLight,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // Show first 3 jobs
          final List<Job> recentJobs = jobs.take(3).toList();

          return Column(
            children: recentJobs
                .map(
                  (Job job) => Padding(
                    padding: const EdgeInsets.only(bottom: AppTheme.spacingSm),
                    child: _buildJobPreviewCard(job),
                  ),
                )
                .toList(),
          );
        },
      );

  Widget _buildJobPreviewCard(Job job) =>
      EnhancedBackgrounds.enhancedCardBackground(
        onTap: () {
          // Navigate to job details
        },
        child: Row(
          children: <Widget>[
            Container(
              width: 4,
              height: 60,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: <Color>[
                    AppTheme.accentCopper,
                    AppTheme.secondaryCopper,
                  ],
                ),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      const Icon(
                        Icons.location_city,
                        size: 16,
                        color: AppTheme.accentCopper,
                      ),
                      const SizedBox(width: AppTheme.spacingXs),
                      Text(
                        'Local ${job.local ?? "N/A"}',
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.primaryNavy,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingSm,
                          vertical: AppTheme.spacingXs,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.successGreen.withValues(alpha: 0.1),
                          borderRadius:
                              BorderRadius.circular(AppTheme.radiusXs),
                        ),
                        child: Text(
                          job.classification ?? 'General',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.successGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    job.jobDescription ?? 'No description available',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
}
