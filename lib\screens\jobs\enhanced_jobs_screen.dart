import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../design_system/app_theme.dart';
import '../../design_system/components/enhanced_backgrounds.dart';
import '../../design_system/components/job_card.dart';
import '../../models/job_model.dart';
import '../../navigation/app_router.dart';
import '../../providers/app_state_provider.dart';
import '../../widgets/notification_badge.dart';

class EnhancedJobsScreen extends StatefulWidget {
  const EnhancedJobsScreen({super.key});

  static String routeName = 'jobs';
  static String routePath = '/jobs';

  @override
  State<EnhancedJobsScreen> createState() => _EnhancedJobsScreenState();
}

class _EnhancedJobsScreenState extends State<EnhancedJobsScreen>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _powerFlowController;
  late AnimationController _filterAnimationController;
  late Animation<double> _powerFlowAnimation;

  String _selectedFilter = 'All Jobs';
  String _searchQuery = '';
  bool _showAdvancedFilters = false;

  // Electrical-themed filter categories
  final List<String> _electricalFilterCategories = <String>[
    'All Jobs',
    'Journeyman Lineman',
    'Journeyman Electrician',
    'Journeyman Wireman',
    'Transmission',
    'Distribution',
    'Substation',
    'Storm Work',
  ];

  @override
  void initState() {
    super.initState();
    _powerFlowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _powerFlowAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _powerFlowController,
        curve: Curves.linear,
      ),
    );

  }

  @override
  void dispose() {
    _searchController.dispose();
    _powerFlowController.dispose();
    _filterAnimationController.dispose();
    super.dispose();
  }

  void _toggleAdvancedFilters() {
    setState(() {
      _showAdvancedFilters = !_showAdvancedFilters;
      if (_showAdvancedFilters) {
        _filterAnimationController.forward();
      } else {
        _filterAnimationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        backgroundColor: AppTheme.offWhite,
        appBar: EnhancedBackgrounds.enhancedAppBar(
          title: 'Job Listings',
          actions: <Widget>[
            IconButton(
              icon: Icon(
                _showAdvancedFilters
                    ? Icons.filter_alt
                    : Icons.filter_alt_outlined,
                color: _showAdvancedFilters
                    ? AppTheme.accentCopper
                    : AppTheme.white,
              ),
              onPressed: _toggleAdvancedFilters,
            ),
            NotificationBadge(
              iconColor: AppTheme.white,
              showPopupOnTap: false,
              onTap: () => context.push(AppRouter.notifications),
            ),
          ],
        ),
        body: EnhancedBackgrounds.circuitPatternBackground(
          opacity: 0.02,
          child: Column(
            children: <Widget>[
              // Enhanced Search Bar
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: <Color>[
                      AppTheme.primaryNavy.withValues(alpha: 0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                child: _buildEnhancedSearchBar(),
              ),

              // Animated Filter Section
              AnimatedSize(
                duration: const Duration(milliseconds: 300),
                child: _showAdvancedFilters
                    ? _buildFilterSection()
                    : const SizedBox.shrink(),
              ),

              // Jobs List
              Expanded(
                child: Consumer<AppStateProvider>(
                  builder: (
                    BuildContext context,
                    AppStateProvider appStateProvider,
                    _,
                  ) {
                    final List<Job> jobs = _filterJobs(appStateProvider.jobs);

                    if (appStateProvider.isLoadingJobs) {
                      return _buildElectricalLoadingIndicator();
                    }

                    if (jobs.isEmpty) {
                      return _buildElectricalEmptyState();
                    }

                    return RefreshIndicator(
                      onRefresh: () => appStateProvider.refreshJobs(),
                      color: AppTheme.accentCopper,
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingMd,
                          vertical: AppTheme.spacingSm,
                        ),
                        itemCount: jobs.length,
                        itemBuilder: (BuildContext context, int index) =>
                            _buildEnhancedJobCard(jobs[index], index),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );

  Widget _buildEnhancedSearchBar() => DecoratedBox(
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(AppTheme.radiusMd),
          boxShadow: const <BoxShadow>[AppTheme.shadowMd],
          border: Border.all(
            color: AppTheme.accentCopper.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingSm),
              decoration: const BoxDecoration(
                gradient: AppTheme.buttonGradient,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.radiusMd),
                  bottomLeft: Radius.circular(AppTheme.radiusMd),
                ),
              ),
              child: const Icon(
                Icons.search,
                color: AppTheme.white,
                size: 24,
              ),
            ),
            Expanded(
              child: TextField(
                controller: _searchController,
                onChanged: (String value) {
                  setState(() {
                    _searchQuery = value.toLowerCase();
                  });
                },
                decoration: InputDecoration(
                  hintText:
                      'Search by location, classification, or description...',
                  hintStyle: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textLight,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingMd,
                    vertical: AppTheme.spacingMd,
                  ),
                ),
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textPrimary,
                ),
              ),
            ),
            if (_searchController.text.isNotEmpty)
              IconButton(
                icon: const Icon(
                  Icons.clear,
                  color: AppTheme.textSecondary,
                ),
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                    _searchQuery = '';
                  });
                },
              ),
          ],
        ),
      );

  Widget _buildFilterSection() => Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMd,
          vertical: AppTheme.spacingSm,
        ),
        decoration: BoxDecoration(
          color: AppTheme.white.withValues(alpha: 0.95),
          border: const Border(
            bottom: BorderSide(
              color: AppTheme.borderLight,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Quick Filters',
              style: AppTheme.bodyLarge.copyWith(
                color: AppTheme.primaryNavy,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Wrap(
              spacing: AppTheme.spacingSm,
              runSpacing: AppTheme.spacingSm,
              children: _electricalFilterCategories.map((String filter) {
                final bool isSelected = _selectedFilter == filter;
                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedFilter = filter;
                      });
                    },
                    borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingMd,
                        vertical: AppTheme.spacingSm,
                      ),
                      decoration: BoxDecoration(
                        gradient: isSelected ? AppTheme.buttonGradient : null,
                        color: isSelected ? null : AppTheme.lightGray,
                        borderRadius:
                            BorderRadius.circular(AppTheme.radiusRound),
                        border: Border.all(
                          color: isSelected
                              ? AppTheme.accentCopper
                              : AppTheme.borderLight,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          if (filter == 'Storm Work')
                            Icon(
                              Icons.thunderstorm,
                              size: 16,
                              color: isSelected
                                  ? AppTheme.white
                                  : AppTheme.warningYellow,
                            ),
                          if (filter == 'Storm Work')
                            const SizedBox(width: AppTheme.spacingXs),
                          Text(
                            filter,
                            style: AppTheme.bodySmall.copyWith(
                              color: isSelected
                                  ? AppTheme.white
                                  : AppTheme.textPrimary,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      );

  Widget _buildElectricalLoadingIndicator() => Center(
        child: EnhancedBackgrounds.sparkEffectBackground(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              AnimatedBuilder(
                animation: _powerFlowAnimation,
                builder: (BuildContext context, Widget? child) => Stack(
                  alignment: Alignment.center,
                  children: <Widget>[
                    // Outer ring
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppTheme.accentCopper.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                    ),
                    // Animated power flow
                    Transform.rotate(
                      angle: _powerFlowAnimation.value * 2 * 3.14159,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: SweepGradient(
                            colors: <Color>[
                              AppTheme.accentCopper.withValues(alpha: 0),
                              AppTheme.accentCopper,
                              AppTheme.accentCopper.withValues(alpha: 0),
                            ],
                            stops: const <double>[0, 0.5, 1],
                          ),
                        ),
                      ),
                    ),
                    // Center icon
                    Container(
                      width: 50,
                      height: 50,
                      decoration: const BoxDecoration(
                        color: AppTheme.primaryNavy,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.electrical_services,
                        color: AppTheme.white,
                        size: 28,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppTheme.spacingLg),
              Text(
                'Connecting to Power Grid...',
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.primaryNavy,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: AppTheme.spacingSm),
              Text(
                'Loading available jobs',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );

  Widget _buildElectricalEmptyState() => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Container(
              width: 120,
              height: 120,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: <Color>[
                    AppTheme.lightGray,
                    AppTheme.offWhite,
                  ],
                ),
                shape: BoxShape.circle,
                boxShadow: <BoxShadow>[AppTheme.shadowLg],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: <Widget>[
                  Icon(
                    Icons.electrical_services_outlined,
                    size: 60,
                    color: AppTheme.textLight.withValues(alpha: 0.5),
                  ),
                  Positioned(
                    bottom: 25,
                    right: 25,
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: const BoxDecoration(
                        color: AppTheme.errorRed,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: AppTheme.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppTheme.spacingLg),
            Text(
              _searchQuery.isNotEmpty || _selectedFilter != 'All Jobs'
                  ? 'No jobs match your criteria'
                  : 'No Power on the Grid',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.primaryNavy,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              _searchQuery.isNotEmpty || _selectedFilter != 'All Jobs'
                  ? 'Try adjusting your filters or search terms'
                  : 'Check back soon for new job opportunities',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLg),
            if (_searchQuery.isNotEmpty || _selectedFilter != 'All Jobs')
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                    _searchQuery = '';
                    _selectedFilter = 'All Jobs';
                  });
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Clear Filters'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentCopper,
                  foregroundColor: AppTheme.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingLg,
                    vertical: AppTheme.spacingMd,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                  ),
                ),
              ),
          ],
        ),
      );

  Widget _buildEnhancedJobCard(Job job, int index) {
    // Add staggered animation
    final delay = index * 50;

    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + delay),
      tween: Tween(begin: 0, end: 1),
      curve: Curves.easeOutBack,
      builder: (BuildContext context, double value, Widget? child) =>
          Transform.scale(
        scale: value,
        child: Opacity(
          opacity: value,
          child: child,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(bottom: AppTheme.spacingSm),
        child: JobCard(
          job: job,
          variant: JobCardVariant.full,
          onTap: () {
            // Navigate to job details
          },
          onBidNow: () {
            // Handle bid action
          },
          onFavorite: () {
            // Handle favorite action
          },
        ),
      ),
    );
  }

  List<Job> _filterJobs(List<Job> jobs) => jobs.where((Job job) {
        // Apply search filter
        if (_searchQuery.isNotEmpty) {
          final String searchLower = _searchQuery.toLowerCase();
          final bool matchesSearch =
              (job.local?.toString().toLowerCase().contains(searchLower) ??
                      false) ||
                  (job.classification?.toLowerCase().contains(searchLower) ??
                      false) ||
                  (job.jobDescription?.toLowerCase().contains(searchLower) ??
                      false) ||
                  (job.location.toLowerCase().contains(searchLower));

          if (!matchesSearch) return false;
        }

        // Apply category filter
        if (_selectedFilter != 'All Jobs') {
          if (_selectedFilter == 'Storm Work') {
            return job.classification?.toLowerCase().contains('storm') ?? false;
          }
          return job.classification?.contains(_selectedFilter) ?? false;
        }

        return true;
      }).toList();
}
