import 'package:flutter/material.dart';
import '../app_theme.dart';
import '../../models/job_model.dart';
import 'reusable_components.dart';
import 'enhanced_backgrounds.dart';

/// Enhanced JobCard component with electrical theme
class EnhancedJobCard extends StatelessWidget {
  final Job job;
  final JobCardVariant variant;
  final VoidCallback? onTap;
  final VoidCallback? onViewDetails;
  final VoidCallback? onBidNow;
  final VoidCallback? onFavorite;
  final bool isFavorited;
  final EdgeInsets? margin;
  final EdgeInsets? padding;

  const EnhancedJobCard({
    super.key,
    required this.job,
    required this.variant,
    this.onTap,
    this.onViewDetails,
    this.onBidNow,
    this.onFavorite,
    this.isFavorited = false,
    this.margin,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    switch (variant) {
      case JobCardVariant.half:
        return _buildHalfCard();
      case JobCardVariant.full:
        return _buildFullCard();
    }
  }

  Widget _buildHalfCard() {
    return EnhancedBackgrounds.enhancedCardBackground(
      onTap: onTap,
      margin: margin ?? const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSm,
        vertical: AppTheme.spacingXs,
      ),
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingMd),
      showCircuitPattern: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          const SizedBox(height: AppTheme.spacingSm),
          _buildContent(),
          const SizedBox(height: AppTheme.spacingMd),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildFullCard() {
    return EnhancedBackgrounds.enhancedCardBackground(
      onTap: onTap,
      margin: margin ?? const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSm,
        vertical: AppTheme.spacingXs,
      ),
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingMd),
      showCircuitPattern: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEnhancedHeader(),
          const SizedBox(height: AppTheme.spacingSm),
          _buildStatusIndicator(),
          const SizedBox(height: AppTheme.spacingMd),
          _buildEnhancedContent(),
          const SizedBox(height: AppTheme.spacingMd),
          _buildElectricalDetails(),
          const SizedBox(height: AppTheme.spacingMd),
          _buildEnhancedActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Local indicator with electrical theme
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSm,
            vertical: AppTheme.spacingXs,
          ),
          decoration: BoxDecoration(
            gradient: AppTheme.buttonGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusXs),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.electrical_services,
                size: 12,
                color: AppTheme.white,
              ),
              const SizedBox(width: AppTheme.spacingXs),
              Text(
                'Local ${job.local ?? "N/A"}',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        const Spacer(),
        if (job.isStormWork ?? false)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingSm,
              vertical: AppTheme.spacingXs,
            ),
            decoration: BoxDecoration(
              color: AppTheme.warningYellow.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusXs),
              border: Border.all(
                color: AppTheme.warningYellow,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.thunderstorm,
                  size: 12,
                  color: AppTheme.warningYellow,
                ),
                const SizedBox(width: AppTheme.spacingXs),
                Text(
                  'STORM WORK',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.warningYellow,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildEnhancedHeader() {
    return Row(
      children: [
        // Enhanced local indicator
        Container(
          height: 50,
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingMd,
          ),
          decoration: BoxDecoration(
            gradient: AppTheme.buttonGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            boxShadow: [AppTheme.shadowSm],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppTheme.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.electrical_services,
                  size: 18,
                  color: AppTheme.white,
                ),
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'IBEW Local',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.white.withValues(alpha: 0.8),
                      fontSize: 10,
                    ),
                  ),
                  Text(
                    '${job.local ?? "N/A"}',
                    style: AppTheme.bodyLarge.copyWith(
                      color: AppTheme.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const Spacer(),
        // Favorite button with electrical animation
        _buildFavoriteButton(),
      ],
    );
  }

  Widget _buildStatusIndicator() {
    final isUrgent = job.isStormWork ?? false;
    final isPriority = job.isHighPriority ?? false;
    
    if (!isUrgent && !isPriority) return const SizedBox.shrink();
    
    return Container(
      decoration: EnhancedBackgrounds.voltageStatusGradient(
        isUrgent ? VoltageLevel.high : VoltageLevel.medium,
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMd,
        vertical: AppTheme.spacingSm,
      ),
      child: Row(
        children: [
          Icon(
            isUrgent ? Icons.thunderstorm : Icons.priority_high,
            size: 16,
            color: AppTheme.white,
          ),
          const SizedBox(width: AppTheme.spacingSm),
          Text(
            isUrgent ? 'EMERGENCY STORM WORK' : 'HIGH PRIORITY',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.white,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
          const Spacer(),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppTheme.white,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (job.classification != null)
          Text(
            job.classification!,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.primaryNavy,
              fontWeight: FontWeight.w600,
            ),
          ),
        if (job.description != null) ...[
          const SizedBox(height: AppTheme.spacingXs),
          Text(
            job.description!,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildEnhancedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Classification with electrical icon
        if (job.classification != null)
          Row(
            children: [
              Icon(
                _getClassificationIcon(job.classification!),
                size: 20,
                color: AppTheme.accentCopper,
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: Text(
                  job.classification!,
                  style: AppTheme.headlineSmall.copyWith(
                    color: AppTheme.primaryNavy,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        
        // Location with enhanced styling
        if (job.location != null) ...[
          const SizedBox(height: AppTheme.spacingSm),
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 16,
                color: AppTheme.textSecondary,
              ),
              const SizedBox(width: AppTheme.spacingXs),
              Expanded(
                child: Text(
                  job.location!,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        ],
        
        // Description with better formatting
        if (job.description != null) ...[
          const SizedBox(height: AppTheme.spacingMd),
          Text(
            job.description!,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textPrimary,
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildElectricalDetails() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.offWhite,
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
        border: Border.all(
          color: AppTheme.accentCopper.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          _buildDetailItem(
            icon: Icons.schedule,
            label: 'Duration',
            value: job.duration ?? 'TBD',
          ),
          const SizedBox(width: AppTheme.spacingLg),
          _buildDetailItem(
            icon: Icons.attach_money,
            label: 'Rate',
            value: job.rate ?? 'Contact Local',
          ),
          const Spacer(),
          // Voltage level indicator
          _buildVoltageIndicator(),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 14,
              color: AppTheme.accentCopper,
            ),
            const SizedBox(width: AppTheme.spacingXs),
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingXs),
        Text(
          value,
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.primaryNavy,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildVoltageIndicator() {
    final voltage = _getVoltageLevel();
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSm,
        vertical: AppTheme.spacingXs,
      ),
      decoration: EnhancedBackgrounds.voltageStatusGradient(voltage),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.bolt,
            size: 14,
            color: AppTheme.white,
          ),
          const SizedBox(width: AppTheme.spacingXs),
          Text(
            _getVoltageText(voltage),
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: onViewDetails,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.lightGray,
              foregroundColor: AppTheme.primaryNavy,
              elevation: 0,
              padding: const EdgeInsets.symmetric(
                vertical: AppTheme.spacingSm,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              ),
            ),
            child: const Text('Details'),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSm),
        Expanded(
          child: ElevatedButton(
            onPressed: onBidNow,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentCopper,
              foregroundColor: AppTheme.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(
                vertical: AppTheme.spacingSm,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              ),
            ),
            child: const Text('Bid Now'),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedActionButtons() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: onBidNow,
            icon: const Icon(Icons.flash_on, size: 18),
            label: const Text('Quick Bid'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentCopper,
              foregroundColor: AppTheme.white,
              elevation: 2,
              shadowColor: AppTheme.accentCopper.withValues(alpha: 0.3),
              padding: const EdgeInsets.symmetric(
                vertical: AppTheme.spacingMd,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              ),
            ),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSm),
        Expanded(
          child: OutlinedButton(
            onPressed: onViewDetails,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryNavy,
              side: BorderSide(
                color: AppTheme.primaryNavy,
                width: 1.5,
              ),
              padding: const EdgeInsets.symmetric(
                vertical: AppTheme.spacingMd,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              ),
            ),
            child: const Text('Details'),
          ),
        ),
      ],
    );
  }

  Widget _buildFavoriteButton() {
    return Container(
      decoration: BoxDecoration(
        color: isFavorited ? AppTheme.errorRed : AppTheme.lightGray,
        shape: BoxShape.circle,
        boxShadow: [AppTheme.shadowSm],
      ),
      child: IconButton(
        onPressed: onFavorite,
        icon: Icon(
          isFavorited ? Icons.favorite : Icons.favorite_border,
          color: isFavorited ? AppTheme.white : AppTheme.textSecondary,
          size: 20,
        ),
      ),
    );
  }

  IconData _getClassificationIcon(String classification) {
    if (classification.toLowerCase().contains('lineman')) {
      return Icons.power_outlined;
    } else if (classification.toLowerCase().contains('electrician')) {
      return Icons.electrical_services;
    } else if (classification.toLowerCase().contains('wireman')) {
      return Icons.cable;
    } else if (classification.toLowerCase().contains('operator')) {
      return Icons.settings;
    }
    return Icons.construction;
  }

  VoltageLevel _getVoltageLevel() {
    final classification = job.classification?.toLowerCase() ?? '';
    if (classification.contains('transmission') || 
        classification.contains('lineman')) {
      return VoltageLevel.high;
    } else if (classification.contains('distribution') || 
               classification.contains('substation')) {
      return VoltageLevel.medium;
    }
    return VoltageLevel.low;
  }

  String _getVoltageText(VoltageLevel level) {
    switch (level) {
      case VoltageLevel.high:
        return 'HIGH V';
      case VoltageLevel.medium:
        return 'MED V';
      case VoltageLevel.low:
        return 'LOW V';
    }
  }
}

/// Job card variants for different use cases
enum JobCardVariant {
  /// Compact card for home screen and lists
  half,
  /// Full detailed card for job listings
  full,
}
