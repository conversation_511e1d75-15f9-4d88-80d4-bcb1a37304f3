import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../app_theme.dart';

/// Enhanced electrical-themed background components for the app
class EnhancedBackgrounds {
  EnhancedBackgrounds._();

  /// Circuit pattern background with animated electricity flow
  static Widget circuitPatternBackground({
    required Widget child,
    double opacity = 0.05,
    Color? patternColor,
    bool animated = false,
  }) {
    return Stack(
      children: [
        Positioned.fill(
          child: RepaintBoundary(
            child: CustomPaint(
              painter: CircuitPatternPainter(
                color: patternColor ?? AppTheme.accentCopper,
                opacity: opacity,
                animated: animated,
              ),
            ),
          ),
        ),
        child,
      ],
    );
  }

  /// Gradient background with electrical theme
  static Widget electricalGradient({
    required Widget child,
    List<Color>? colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: begin,
          end: end,
          colors: colors ?? [
            AppTheme.primaryNavy.withValues(alpha: 0.95),
            AppTheme.secondaryNavy,
          ],
        ),
      ),
      child: child,
    );
  }

  /// Spark effect background for loading states
  static Widget sparkEffectBackground({
    required Widget child,
    bool active = true,
  }) {
    return Stack(
      children: [
        if (active)
          Positioned.fill(
            child: RepaintBoundary(
              child: SparkAnimation(),
            ),
          ),
        child,
      ],
    );
  }

  /// Enhanced card background with subtle circuit pattern
  static Widget enhancedCardBackground({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    bool showCircuitPattern = true,
  }) {
    return Container(
      margin: margin,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.radiusMd),
          child: Container(
            padding: padding ?? const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              border: Border.all(
                color: AppTheme.borderLight,
                width: AppTheme.borderWidthThin,
              ),
              boxShadow: [AppTheme.shadowMd],
            ),
            child: showCircuitPattern
                ? Stack(
                    children: [
                      Positioned.fill(
                        child: CustomPaint(
                          painter: SubtleCircuitPainter(),
                        ),
                      ),
                      child,
                    ],
                  )
                : child,
          ),
        ),
      ),
    );
  }

  /// Toolbar gradient for app bars
  static PreferredSizeWidget enhancedAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = false,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryNavy,
              AppTheme.secondaryNavy.withValues(alpha: 0.9),
            ],
          ),
        ),
      ),
      title: Row(
        mainAxisSize: centerTitle ? MainAxisSize.min : MainAxisSize.max,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: AppTheme.buttonGradient,
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: const EdgeInsets.all(6),
              child: Icon(
                Icons.electrical_services,
                size: 20,
                color: AppTheme.white,
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSm),
          Text(
            title,
            style: AppTheme.headlineMedium.copyWith(color: AppTheme.white),
          ),
        ],
      ),
      centerTitle: centerTitle,
      leading: leading,
      actions: actions,
      bottom: bottom,
      backgroundColor: Colors.transparent,
      elevation: 0,
    );
  }

  /// Status gradient for different voltage levels
  static BoxDecoration voltageStatusGradient(VoltageLevel level) {
    List<Color> colors;
    switch (level) {
      case VoltageLevel.low:
        colors = [AppTheme.successGreen, AppTheme.successGreen.withValues(alpha: 0.8)];
        break;
      case VoltageLevel.medium:
        colors = [AppTheme.warningYellow, AppTheme.secondaryCopper];
        break;
      case VoltageLevel.high:
        colors = [AppTheme.errorRed, AppTheme.errorRed.withValues(alpha: 0.8)];
        break;
    }
    
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: colors,
      ),
      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
    );
  }
}

/// Voltage level enum for status indicators
enum VoltageLevel { low, medium, high }

/// Circuit pattern painter for backgrounds
class CircuitPatternPainter extends CustomPainter {
  final Color color;
  final double opacity;
  final bool animated;

  CircuitPatternPainter({
    required this.color,
    required this.opacity,
    this.animated = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: opacity)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final dotPaint = Paint()
      ..color = color.withValues(alpha: opacity * 2)
      ..style = PaintingStyle.fill;

    // Draw circuit lines
    const spacing = 40.0;
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw connection points
    for (double x = spacing; x < size.width; x += spacing) {
      for (double y = spacing; y < size.height; y += spacing) {
        canvas.drawCircle(Offset(x, y), 3, dotPaint);
      }
    }
  }

  @override
  bool shouldRepaint(CircuitPatternPainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.opacity != opacity ||
        oldDelegate.animated != animated;
  }
}

/// Subtle circuit pattern for cards
class SubtleCircuitPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.accentCopper.withValues(alpha: 0.03)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // Draw subtle corner patterns
    final path = Path();
    
    // Top-left corner
    path.moveTo(0, 20);
    path.lineTo(20, 20);
    path.lineTo(20, 0);
    
    // Top-right corner
    path.moveTo(size.width - 20, 0);
    path.lineTo(size.width - 20, 20);
    path.lineTo(size.width, 20);
    
    // Bottom-left corner
    path.moveTo(0, size.height - 20);
    path.lineTo(20, size.height - 20);
    path.lineTo(20, size.height);
    
    // Bottom-right corner
    path.moveTo(size.width - 20, size.height);
    path.lineTo(size.width - 20, size.height - 20);
    path.lineTo(size.width, size.height - 20);
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(SubtleCircuitPainter oldDelegate) => false;
}

/// Animated spark effect for loading states
class SparkAnimation extends StatefulWidget {
  const SparkAnimation({super.key});

  @override
  State<SparkAnimation> createState() => _SparkAnimationState();
}

class _SparkAnimationState extends State<SparkAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Spark> _sparks;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
    
    _sparks = List.generate(
      5,
      (index) => Spark(
        position: Offset(
          math.Random().nextDouble(),
          math.Random().nextDouble(),
        ),
        delay: math.Random().nextDouble(),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: SparkPainter(
            sparks: _sparks,
            animation: _controller.value,
          ),
        );
      },
    );
  }
}

/// Individual spark data
class Spark {
  final Offset position;
  final double delay;

  Spark({required this.position, required this.delay});
}

/// Spark painter for animation
class SparkPainter extends CustomPainter {
  final List<Spark> sparks;
  final double animation;

  SparkPainter({required this.sparks, required this.animation});

  @override
  void paint(Canvas canvas, Size size) {
    for (final spark in sparks) {
      final adjustedAnimation = (animation + spark.delay) % 1.0;
      final opacity = (1.0 - adjustedAnimation) * 0.3;
      
      final paint = Paint()
        ..color = AppTheme.accentCopper.withValues(alpha: opacity)
        ..style = PaintingStyle.fill;
      
      final center = Offset(
        spark.position.dx * size.width,
        spark.position.dy * size.height,
      );
      
      final radius = adjustedAnimation * 20;
      canvas.drawCircle(center, radius, paint);
      
      // Draw spark lines
      final linePaint = Paint()
        ..color = AppTheme.accentCopper.withValues(alpha: opacity * 0.5)
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;
      
      for (int i = 0; i < 8; i++) {
        final angle = (i * math.pi / 4) + (adjustedAnimation * math.pi);
        final endPoint = Offset(
          center.dx + math.cos(angle) * radius * 1.5,
          center.dy + math.sin(angle) * radius * 1.5,
        );
        canvas.drawLine(center, endPoint, linePaint);
      }
    }
  }

  @override
  bool shouldRepaint(SparkPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}
