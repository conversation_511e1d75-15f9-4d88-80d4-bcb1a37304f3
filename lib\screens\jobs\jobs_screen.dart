import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../design_system/app_theme.dart';
import '../../design_system/components/reusable_components.dart';
import '../../design_system/popup_theme.dart';
import '../../models/job_model.dart';
import '../../navigation/app_router.dart';
import '../../providers/app_state_provider.dart';
import '../../utils/job_formatting.dart';
import '../../widgets/notification_badge.dart';
import '../../widgets/optimized_selector_widgets.dart';

class JobsScreen extends StatefulWidget {
  const JobsScreen({super.key});

  static String routeName = 'jobs';
  static String routePath = '/jobs';

  @override
  State<JobsScreen> createState() => _JobsScreenState();
}

class _JobsScreenState extends State<JobsScreen> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _powerFlowController;
  late Animation<double> _powerFlowAnimation;
  String _selectedFilter = 'All Jobs';
  String _searchQuery = '';
  bool _showAdvancedFilters = false;

  // Electrical-themed filter categories
  final List<String> _electricalFilterCategories = <String>[
    'All Jobs',
    'Journeyman Lineman',
    'Journeyman Electrician',
    'Journeyman Wireman',
    'Transmission',
    'Distribution',
    'Substation',
    'Storm Work',
  ];

  @override
  void initState() {
    super.initState();
    _powerFlowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    _powerFlowAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _powerFlowController,
        curve: Curves.linear,
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _powerFlowController.dispose();
    super.dispose();
  }

  Widget _buildElectricalLoadingIndicator() => AnimatedBuilder(
        animation: _powerFlowAnimation,
        builder: (BuildContext context, Widget? child) => Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Stack(
              alignment: Alignment.center,
              children: <Widget>[
                // Power grid background
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppTheme.accentCopper.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                ),
                // Animated power flow
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: <Color>[
                        AppTheme.accentCopper
                            .withValues(alpha: _powerFlowAnimation.value),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
                // Center electrical icon
                const Icon(
                  Icons.electrical_services,
                  size: 32,
                  color: AppTheme.primaryNavy,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMd),
            Text(
              'Connecting to Power Grid...',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      );

  Widget _buildElectricalEmptyState() => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            width: 120,
            height: 120,
            decoration: const BoxDecoration(
              gradient: AppTheme.cardGradient,
              shape: BoxShape.circle,
              boxShadow: <BoxShadow>[AppTheme.shadowMd],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                // Power line tower silhouette
                Icon(
                  Icons.electrical_services_outlined,
                  size: 60,
                  color: AppTheme.primaryNavy.withValues(alpha: 0.3),
                ),
                // Power off indicator
                Positioned(
                  bottom: 25,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingXs,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.textLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'OFF',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.white,
                        fontSize: 8,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          Text(
            'No Power Jobs Available',
            style: AppTheme.headlineSmall.copyWith(
              color: AppTheme.primaryNavy,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Text(
            'Check back later for new electrical opportunities',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );

  Widget _buildElectricalFilterButton(String filter) {
    final isSelected = _selectedFilter == filter;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = filter;
        });
        // Trigger a refresh with the new filter
        context.read<AppStateProvider>().refreshJobs();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMd,
          vertical: AppTheme.spacingSm,
        ),
        decoration: BoxDecoration(
          gradient: isSelected ? AppTheme.buttonGradient : null,
          color: isSelected ? null : AppTheme.lightGray,
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          border: isSelected
              ? Border.all(color: AppTheme.accentCopper)
              : Border.all(color: AppTheme.lightGray),
          boxShadow: isSelected ? <BoxShadow>[AppTheme.shadowSm] : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            if (isSelected) ...<Widget>[
              Icon(
                _getFilterIcon(filter),
                size: 16,
                color: AppTheme.white,
              ),
              const SizedBox(width: AppTheme.spacingXs),
            ],
            Text(
              filter,
              style: AppTheme.bodyMedium.copyWith(
                color: isSelected ? AppTheme.white : AppTheme.textPrimary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getFilterIcon(String filter) {
    switch (filter) {
      case 'Transmission':
        return Icons.electrical_services;
      case 'Distribution':
        return Icons.power_outlined;
      case 'Substation':
        return Icons.electrical_services_outlined;
      case 'Storm Work':
        return Icons.flash_on;
      default:
        return Icons.work;
    }
  }

  Color _getVoltageLevelColor(String? voltageLevel) {
    if (voltageLevel == null) return AppTheme.textSecondary;

    // Since voltage levels are not standard classifications in the trade,
    // we'll return a default color for all cases
    return AppTheme.textSecondary;
  }

  Widget _buildElectricalJobCard(Job job) {
    final voltageColor = _getVoltageLevelColor(job.voltageLevel);
    final isEmergency =
        job.classification?.toLowerCase().contains('storm') ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        boxShadow: const <BoxShadow>[AppTheme.shadowMd],
        border: isEmergency
            ? Border.all(
                color: AppTheme.errorRed,
                width: AppTheme.borderWidthThick,
              )
            : Border.all(color: AppTheme.accentCopper.withValues(alpha: 0.3)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          onTap: () => _showElectricalJobDetails(job),
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // Header row with classification and voltage indicator
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    // Left side - Classification and Local
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          if (isEmergency)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingSm,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.errorRed,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  const Icon(
                                    Icons.flash_on,
                                    size: 12,
                                    color: AppTheme.white,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    'EMERGENCY',
                                    style: AppTheme.labelSmall.copyWith(
                                      color: AppTheme.white,
                                      fontSize: 9,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (isEmergency)
                            const SizedBox(height: AppTheme.spacingXs),
                          Row(
                            children: <Widget>[
                              Text(
                                'Local: ',
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.textSecondary,
                                ),
                              ),
                              Text(
                                job.localNumber?.toString() ??
                                    job.local?.toString() ??
                                    'N/A',
                                style: AppTheme.titleMedium.copyWith(
                                  color: AppTheme.primaryNavy,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppTheme.spacingXs),
                          Text(
                            JobFormatting.formatJobTitle(
                              job.jobTitle ??
                                  job.jobClass ??
                                  job.classification ??
                                  'Electrical Worker',
                            ),
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.primaryNavy,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    // Right side - Voltage level indicator
                    if (job.voltageLevel != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingSm,
                          vertical: AppTheme.spacingXs,
                        ),
                        decoration: BoxDecoration(
                          color: voltageColor.withValues(alpha: 0.1),
                          borderRadius:
                              BorderRadius.circular(AppTheme.radiusSm),
                          border: Border.all(color: voltageColor),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Icon(
                              Icons.bolt,
                              size: 14,
                              color: voltageColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              job.voltageLevel!,
                              style: AppTheme.labelSmall.copyWith(
                                color: voltageColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: AppTheme.spacingMd),

                // Job details in two columns
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    // Left column
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          _buildJobDetailRow(
                            Icons.access_time,
                            'Posted:',
                            job.datePosted ?? 'Recently',
                          ),
                          const SizedBox(height: AppTheme.spacingXs),
                          _buildJobDetailRow(
                            Icons.location_on,
                            'Location:',
                            JobFormatting.formatLocation(job.location),
                          ),
                          const SizedBox(height: AppTheme.spacingXs),
                          _buildJobDetailRow(
                            Icons.business,
                            'Company:',
                            job.company,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMd),
                    // Right column
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          _buildJobDetailRow(
                            Icons.schedule,
                            'Hours:',
                            job.hours != null
                                ? JobFormatting.formatHours(job.hours)
                                : '40hrs',
                          ),
                          const SizedBox(height: AppTheme.spacingXs),
                          _buildJobDetailRow(
                            Icons.attach_money,
                            'Wage:',
                            job.wage != null
                                ? JobFormatting.formatWage(job.wage)
                                : 'Competitive',
                          ),
                          const SizedBox(height: AppTheme.spacingXs),
                          if (job.perDiem != null)
                            _buildJobDetailRow(
                              Icons.card_giftcard,
                              'Per Diem:',
                              job.perDiem!,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Add certifications if qualifications field exists
                if (job.qualifications != null &&
                    job.qualifications!.isNotEmpty) ...<Widget>[
                  const SizedBox(height: AppTheme.spacingMd),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingSm,
                      vertical: AppTheme.spacingXs,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.accentCopper.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        const Icon(
                          Icons.verified_user,
                          size: 14,
                          color: AppTheme.accentCopper,
                        ),
                        const SizedBox(width: AppTheme.spacingXs),
                        Expanded(
                          child: Text(
                            'Requires: ${job.qualifications}',
                            style: AppTheme.labelSmall.copyWith(
                              color: AppTheme.textPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // Add start date if available
                if (job.startDate != null) ...<Widget>[
                  const SizedBox(height: AppTheme.spacingXs),
                  Row(
                    children: <Widget>[
                      const Icon(
                        Icons.calendar_today,
                        size: 14,
                        color: AppTheme.textSecondary,
                      ),
                      const SizedBox(width: AppTheme.spacingXs),
                      Text(
                        'Start: ${job.startDate}',
                        style: AppTheme.labelSmall.copyWith(
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: AppTheme.spacingMd),

                // Action buttons with electrical theme
                Row(
                  children: <Widget>[
                    Expanded(
                      child: JJSecondaryButton(
                        text: 'Details',
                        icon: Icons.visibility,
                        onPressed: () => _showElectricalJobDetails(job),
                        height: 42,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMd),
                    Expanded(
                      child: JJPrimaryButton(
                        text: 'Apply',
                        icon: Icons.send,
                        onPressed: () => _handleBidNow(job),
                        height: 42,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildJobDetailRow(IconData icon, String label, String value) => Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Icon(
            icon,
            size: 14,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(width: AppTheme.spacingXs),
          Expanded(
            child: RichText(
              text: TextSpan(
                children: <InlineSpan>[
                  TextSpan(
                    text: '$label ',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  TextSpan(
                    text: value,
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );

  void _showElectricalJobDetails(Job job) {
    showDialog<void>(
      context: context,
      barrierColor: PopupThemeData.wide().barrierColor ?? Colors.black54,
      builder: (BuildContext context) => PopupTheme(
        data: PopupThemeData.wide(),
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.all(AppTheme.spacingLg),
          child: JobDetailsDialogWidget(job: job),
        ),
      ),
    );
  }

  void _handleBidNow(Job job) {
    _showBidSubmissionDialog(job);
  }

  void _showSearchDialog() {
    showDialog<void>(
      context: context,
      barrierColor: PopupThemeData.alertDialog().barrierColor,
      builder: (BuildContext context) => PopupTheme(
        data: PopupThemeData.alertDialog(),
        child: AlertDialog(
          backgroundColor: PopupThemeData.alertDialog().backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              PopupThemeData.alertDialog().borderRadius,
            ),
            side: BorderSide(
              color: PopupThemeData.alertDialog().borderColor,
              width: PopupThemeData.alertDialog().borderWidth,
            ),
          ),
          elevation: PopupThemeData.alertDialog().elevation,
          contentPadding: PopupThemeData.alertDialog().padding,
          title: Text(
            'Search Jobs',
            style: AppTheme.headlineSmall.copyWith(
              color: AppTheme.primaryNavy,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search by company, location, or classification...',
                  prefixIcon:
                      const Icon(Icons.search, color: AppTheme.primaryNavy),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                    borderSide: const BorderSide(
                      color: AppTheme.accentCopper,
                      width: AppTheme.borderWidthThick,
                    ),
                  ),
                ),
                onChanged: (String value) {
                  setState(() {
                    _searchQuery = value;
                  });
                  // Update the provider search
                  // Handle search locally for now
                },
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                setState(() {
                  _searchQuery = '';
                  _searchController.clear();
                });
                // Refresh jobs
                context.read<AppStateProvider>().refreshJobs();
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.textSecondary,
              ),
              child: const Text('Clear'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryNavy,
                foregroundColor: AppTheme.white,
              ),
              child: const Text('Search'),
            ),
          ],
        ),
      ),
    );
  }

  void _showBidSubmissionDialog(Job job) {
    final messageController = TextEditingController();

    showDialog<void>(
      context: context,
      barrierColor: PopupThemeData.alertDialog().barrierColor,
      builder: (BuildContext context) => PopupTheme(
        data: PopupThemeData.alertDialog(),
        child: AlertDialog(
          backgroundColor: PopupThemeData.alertDialog().backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              PopupThemeData.alertDialog().borderRadius,
            ),
            side: BorderSide(
              color: PopupThemeData.alertDialog().borderColor,
              width: PopupThemeData.alertDialog().borderWidth,
            ),
          ),
          elevation: PopupThemeData.alertDialog().elevation,
          contentPadding: PopupThemeData.alertDialog().padding,
          title: Text(
            'Submit Bid',
            style: AppTheme.headlineSmall.copyWith(
              color: AppTheme.primaryNavy,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'Job: ${job.classification ?? 'Electrical Worker'}',
                style: AppTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryNavy,
                ),
              ),
              Text(
                'Company: ${job.company}',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              Text(
                'Location: ${job.location}',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              const SizedBox(height: AppTheme.spacingMd),
              TextField(
                controller: messageController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'Add a message (optional)...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                    borderSide: const BorderSide(
                      color: AppTheme.accentCopper,
                      width: AppTheme.borderWidthThick,
                    ),
                  ),
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.textSecondary,
              ),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _submitBid(job, messageController.text);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentCopper,
                foregroundColor: AppTheme.white,
              ),
              child: const Text('Submit Bid'),
            ),
          ],
        ),
      ),
    );
  }

  void _submitBid(Job job, String message) {
    // Here you would typically submit to your backend/Firestore
    // For now, we'll just show a success message
    JJSnackBar.showSuccess(
      context: context,
      message: 'Bid submitted for ${job.classification}!',
    );
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        backgroundColor: AppTheme.offWhite,
        body: NestedScrollView(
          floatHeaderSlivers: true,
          headerSliverBuilder: (BuildContext context, _) => <Widget>[
            SliverAppBar(
              pinned: true,
              backgroundColor: AppTheme.primaryNavy,
              automaticallyImplyLeading: false,
              expandedHeight: 120,
              flexibleSpace: FlexibleSpaceBar(
                background: DecoratedBox(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: <Color>[
                        AppTheme.primaryNavy,
                        AppTheme.primaryNavy.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: Stack(
                    children: <Widget>[
                      // Circuit pattern overlay
                      Positioned.fill(
                        child: CustomPaint(
                          painter: ElectricalCircuitPainter(),
                        ),
                      ),
                    ],
                  ),
                ),
                title: Row(
                  children: <Widget>[
                    Container(
                      width: 32,
                      height: 32,
                      decoration: const BoxDecoration(
                        gradient: AppTheme.buttonGradient,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.electrical_services,
                        size: 20,
                        color: AppTheme.white,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingSm),
                    Text(
                      'Power Jobs',
                      style: AppTheme.headlineMedium.copyWith(
                        color: AppTheme.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                IconButton(
                  icon: const Icon(Icons.search, color: AppTheme.white),
                  onPressed: _showSearchDialog,
                ),
                IconButton(
                  icon: const Icon(Icons.filter_alt, color: AppTheme.white),
                  onPressed: () {
                    setState(() {
                      _showAdvancedFilters = !_showAdvancedFilters;
                    });
                  },
                ),
                NotificationBadge(
                  iconColor: AppTheme.white,
                  showPopupOnTap: false,
                  onTap: () {
                    context.push(AppRouter.notifications);
                  },
                ),
              ],
            ),
          ],
          body: SafeArea(
            top: false,
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Filter categories
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _electricalFilterCategories
                          .map(
                            (String filter) => Padding(
                              padding: const EdgeInsets.only(
                                right: AppTheme.spacingSm,
                              ),
                              child: _buildElectricalFilterButton(filter),
                            ),
                          )
                          .toList(),
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingLg),

                  // Advanced filters section
                  if (_showAdvancedFilters) ...<Widget>[
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingMd),
                      decoration: BoxDecoration(
                        color: AppTheme.lightGray.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                        border: Border.all(
                          color: AppTheme.accentCopper.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Row(
                            children: <Widget>[
                              const Icon(
                                Icons.tune,
                                color: AppTheme.primaryNavy,
                                size: 20,
                              ),
                              const SizedBox(width: AppTheme.spacingSm),
                              Text(
                                'Advanced Filters',
                                style: AppTheme.titleMedium.copyWith(
                                  color: AppTheme.primaryNavy,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppTheme.spacingMd),
                          Text(
                            'Search Query: ${_searchQuery.isEmpty ? 'None' : _searchQuery}',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacingSm),
                          Row(
                            children: <Widget>[
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _showSearchDialog,
                                  icon: const Icon(Icons.search, size: 16),
                                  label: const Text('Search Jobs'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.primaryNavy,
                                    foregroundColor: AppTheme.white,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: AppTheme.spacingMd,
                                      vertical: AppTheme.spacingSm,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: AppTheme.spacingSm),
                              ElevatedButton.icon(
                                onPressed: () {
                                  setState(() {
                                    _searchQuery = '';
                                    _searchController.clear();
                                    _selectedFilter = 'All Jobs';
                                  });
                                  // Clear all filters and refresh
                                  context
                                      .read<AppStateProvider>()
                                      .refreshJobs();
                                },
                                icon: const Icon(Icons.clear, size: 16),
                                label: const Text('Clear All'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.textLight,
                                  foregroundColor: AppTheme.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppTheme.spacingMd,
                                    vertical: AppTheme.spacingSm,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMd),
                  ],

                  // Jobs section header
                  Text(
                    _selectedFilter == 'All Jobs'
                        ? 'All Power Jobs'
                        : _selectedFilter,
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingMd),

                  // Jobs list with optimized selector widgets
                  Expanded(
                    child: JobsListStateSelector(
                      builder: (
                        BuildContext context,
                        JobsListState jobsState,
                        Widget? child,
                      ) {
                        if (jobsState.isLoading && jobsState.jobs.isEmpty) {
                          return Center(
                            child: _buildElectricalLoadingIndicator(),
                          );
                        }

                        if (jobsState.error != null) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Icon(
                                  Icons.electrical_services_outlined,
                                  size: 64,
                                  color:
                                      AppTheme.errorRed.withValues(alpha: 0.5),
                                ),
                                const SizedBox(height: AppTheme.spacingMd),
                                Text(
                                  'Power Grid Connection Failed',
                                  style: AppTheme.headlineSmall.copyWith(
                                    color: AppTheme.errorRed,
                                  ),
                                ),
                                const SizedBox(height: AppTheme.spacingSm),
                                Text(
                                  'Unable to load job opportunities',
                                  style: AppTheme.bodyMedium.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                                ),
                                const SizedBox(height: AppTheme.spacingMd),
                                ElevatedButton(
                                  onPressed: () => context
                                      .read<AppStateProvider>()
                                      .refreshJobs(),
                                  child: const Text('Retry'),
                                ),
                              ],
                            ),
                          );
                        }

                        if (jobsState.jobs.isEmpty) {
                          return Center(child: _buildElectricalEmptyState());
                        }

                        // Filter jobs based on selected category and search query
                        List<Job> filteredJobs = _selectedFilter == 'All Jobs'
                            ? jobsState.jobs
                            : jobsState.jobs
                                .where(
                                  (Job job) =>
                                      job.classification
                                              ?.contains(_selectedFilter) ==
                                          true ||
                                      job.voltageLevel == _selectedFilter ||
                                      job.typeOfWork
                                              ?.contains(_selectedFilter) ==
                                          true,
                                )
                                .toList();

                        // Apply search filter if search query exists
                        if (_searchQuery.isNotEmpty) {
                          filteredJobs = filteredJobs.where((Job job) {
                            final String query = _searchQuery.toLowerCase();
                            return job.company.toLowerCase().contains(query) ||
                                job.location.toLowerCase().contains(query) ||
                                (job.classification
                                        ?.toLowerCase()
                                        .contains(query) ??
                                    false) ||
                                (job.typeOfWork
                                        ?.toLowerCase()
                                        .contains(query) ??
                                    false) ||
                                (job.voltageLevel
                                        ?.toLowerCase()
                                        .contains(query) ??
                                    false);
                          }).toList();
                        }

                        if (filteredJobs.isEmpty) {
                          return Center(child: _buildElectricalEmptyState());
                        }

                        // Use ListView.builder for job list
                        return ListView.builder(
                          padding: const EdgeInsets.all(AppTheme.spacingMd),
                          itemCount:
                              filteredJobs.length + (jobsState.hasMore ? 1 : 0),
                          itemBuilder: (BuildContext context, int index) {
                            if (index == filteredJobs.length) {
                              // Loading indicator at the bottom
                              return Center(
                                child: Padding(
                                  padding:
                                      const EdgeInsets.all(AppTheme.spacingMd),
                                  child: jobsState.isLoading
                                      ? const CircularProgressIndicator(
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            AppTheme.accentCopper,
                                          ),
                                        )
                                      : ElevatedButton(
                                          onPressed: () => context
                                              .read<AppStateProvider>()
                                              .loadMoreJobs(),
                                          child: const Text('Load More'),
                                        ),
                                ),
                              );
                            }

                            final Job job = filteredJobs[index];
                            return _buildElectricalJobCard(job);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
}

// Custom painter for electrical circuit patterns
class ElectricalCircuitPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.accentCopper.withValues(alpha: 0.1)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw subtle circuit pattern
    for (int i = 0; i < 5; i++) {
      final y = size.height * (i / 5);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width * 0.3, y),
        paint,
      );
      canvas.drawLine(
        Offset(size.width * 0.7, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class JobDetailsDialogWidget extends StatelessWidget {
  const JobDetailsDialogWidget({required this.job, super.key});
  final Job job;

  @override
  Widget build(BuildContext context) {
    final isEmergency =
        job.classification?.toLowerCase().contains('storm') ?? false;

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        maxWidth: MediaQuery.of(context).size.width * 0.9,
      ),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusXl),
        border: Border.all(
          color: AppTheme.accentCopper,
          width: AppTheme.borderWidthThick,
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppTheme.primaryNavy.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: AppTheme.accentCopper.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // Header with gradient background
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppTheme.spacingLg),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: <Color>[
                  AppTheme.primaryNavy,
                  AppTheme.secondaryNavy,
                ],
                stops: <double>[0, 1],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppTheme.radiusXl),
                topRight: Radius.circular(AppTheme.radiusXl),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppTheme.spacingSm,
                              vertical: AppTheme.spacingXs,
                            ),
                            decoration: BoxDecoration(
                              color: isEmergency
                                  ? AppTheme.errorRed
                                  : AppTheme.accentCopper,
                              borderRadius:
                                  BorderRadius.circular(AppTheme.radiusSm),
                            ),
                            child: const Icon(
                              Icons.flash_on,
                              size: AppTheme.iconSm,
                              color: AppTheme.white,
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacingSm),
                          Expanded(
                            child: Text(
                              JobFormatting.formatJobTitle(
                                job.jobTitle ??
                                    job.jobClass ??
                                    job.classification ??
                                    'Electrical Worker',
                              ),
                              style: AppTheme.headlineMedium.copyWith(
                                color: AppTheme.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingXs),
                      Text(
                        'Local ${job.localNumber?.toString() ?? 'N/A'}',
                        style: AppTheme.bodyLarge.copyWith(
                          color: AppTheme.white.withValues(alpha: 0.9),
                        ),
                      ),
                      if (isEmergency) ...<Widget>[
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          'EMERGENCY WORK',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.errorRed,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: AppTheme.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.close, color: AppTheme.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
          ),

          // Content area
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingLg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Quick Actions section
                  Text(
                    'Quick Actions',
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMd),

                  Row(
                    children: <Widget>[
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.work_outline,
                          label: 'Apply',
                          onTap: () {
                            Navigator.of(context).pop();
                            // Handle apply action
                          },
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMd),
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.bookmark_outline,
                          label: 'Save',
                          onTap: () {
                            // Handle save action
                          },
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMd),
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.share_outlined,
                          label: 'Share',
                          onTap: () {
                            // Handle share action
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingXl),

                  // Job Information section
                  Text(
                    'Job Information',
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMd),

                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingMd),
                    decoration: BoxDecoration(
                      color: AppTheme.lightGray.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                      border: Border.all(
                        color: AppTheme.accentCopper.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Column(
                      children: <Widget>[
                        _buildInfoRow(
                          icon: Icons.business,
                          label: 'Company',
                          value: job.company,
                        ),
                        _buildInfoRow(
                          icon: Icons.location_on,
                          label: 'Location',
                          value: job.location,
                        ),
                        _buildInfoRow(
                          icon: Icons.attach_money,
                          label: 'Wage',
                          value: job.wage != null ? '\$${job.wage}/hr' : 'N/A',
                        ),
                        _buildInfoRow(
                          icon: Icons.access_time,
                          label: 'Hours',
                          value: '${job.hours ?? 'N/A'} hours/week',
                        ),
                        _buildInfoRow(
                          icon: Icons.calendar_today,
                          label: 'Start Date',
                          value: job.startDate ?? 'N/A',
                        ),
                        _buildInfoRow(
                          icon: Icons.timer,
                          label: 'Duration',
                          value: job.duration ?? 'N/A',
                        ),
                        _buildInfoRow(
                          icon: Icons.dining,
                          label: 'Per Diem',
                          value: job.perDiem?.isNotEmpty == true ? 'Yes' : 'No',
                          isLast: true,
                        ),
                      ],
                    ),
                  ),

                  if (job.jobDescription?.isNotEmpty == true) ...<Widget>[
                    const SizedBox(height: AppTheme.spacingLg),
                    Text(
                      'Job Description',
                      style: AppTheme.headlineSmall.copyWith(
                        color: AppTheme.primaryNavy,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMd),
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingMd),
                      decoration: BoxDecoration(
                        color: AppTheme.lightGray.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                        border: Border.all(
                          color: AppTheme.accentCopper.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Text(
                        job.jobDescription!,
                        style: AppTheme.bodyMedium,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) =>
      InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          decoration: BoxDecoration(
            color: AppTheme.accentCopper.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMd),
            border: Border.all(
              color: AppTheme.accentCopper.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: <Widget>[
              Icon(
                icon,
                color: AppTheme.accentCopper,
                size: AppTheme.iconMd,
              ),
              const SizedBox(height: AppTheme.spacingXs),
              Text(
                label,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.accentCopper,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    bool isLast = false,
  }) =>
      Column(
        children: <Widget>[
          Row(
            children: <Widget>[
              Icon(
                icon,
                size: AppTheme.iconSm,
                color: AppTheme.textLight,
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    children: <InlineSpan>[
                      TextSpan(
                        text: '$label: ',
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textLight,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: value,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textDark,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          if (!isLast) ...<Widget>[
            const SizedBox(height: AppTheme.spacingSm),
            Divider(
              color: AppTheme.lightGray.withValues(alpha: 0.5),
              thickness: 1,
            ),
            const SizedBox(height: AppTheme.spacingSm),
          ],
        ],
      );
}
