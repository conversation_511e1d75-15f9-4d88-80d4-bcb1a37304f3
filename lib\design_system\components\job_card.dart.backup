import 'package:flutter/material.dart';
import '../app_theme.dart';
import '../../models/job_model.dart';
import 'reusable_components.dart';

/// Enum for JobCard variants
enum JobCardVariant {
  /// Half-size variant for home screen and compact displays
  half,
  /// Full-size variant for jobs screen with detailed information
  full,
}

/// Reusable JobCard component for displaying job information
/// Supports two variants: half (compact) and full (detailed)
class JobCard extends StatelessWidget {
  /// The job object containing all job data
  final Job job;
  
  /// The variant type determining the card size and information display
  final JobCardVariant variant;
  
  /// Callback function for when the card is tapped
  final VoidCallback? onTap;
  
  /// Callback function for the "View Details" button
  final VoidCallback? onViewDetails;
  
  /// Callback function for the "Bid Now" button
  final VoidCallback? onBidNow;
  
  /// Callback function for favoriting/bookmarking the job
  final VoidCallback? onFavorite;
  
  /// Whether the job is currently favorited
  final bool isFavorited;
  
  /// Optional margin for the card
  final EdgeInsets? margin;
  
  /// Optional padding for the card content
  final EdgeInsets? padding;

  const JobCard({
    super.key,
    required this.job,
    required this.variant,
    this.onTap,
    this.onViewDetails,
    this.onBidNow,
    this.onFavorite,
    this.isFavorited = false,
    this.margin,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    switch (variant) {
      case JobCardVariant.half:
        return _buildHalfCard();
      case JobCardVariant.full:
        return _buildFullCard();
    }
  }

  /// Builds the half-size (compact) variant of the job card
  Widget _buildHalfCard() {
    return JJCard(
      onTap: onTap,
      margin: margin ?? const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSm,
        vertical: AppTheme.spacingXs,
      ),
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header row - Local and Classification
          Row(
            children: [
              // Local
              if (job.local != null)
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: 'Local: ',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(
                          text: '${job.local}',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              
              // Vertical divider
              if (job.local != null && job.classification != null)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingSm),
                  width: 1,
                  height: 12,
                  color: AppTheme.lightGray,
                ),
              
              // Classification
              if (job.classification != null)
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: job.classification!,
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              
              // Favorite button
              if (onFavorite != null)
                GestureDetector(
                  onTap: onFavorite,
                  child: Icon(
                    isFavorited ? Icons.bookmark : Icons.bookmark_border,
                    size: AppTheme.iconSm,
                    color: isFavorited ? AppTheme.accentCopper : AppTheme.textLight,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacingSm),
          
          // Posted time row
          if (job.datePosted != null)
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: AppTheme.iconXs,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(width: AppTheme.spacingXs),
                Text(
                  'Posted ${job.datePosted}',
                  style: AppTheme.labelSmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          
          const SizedBox(height: AppTheme.spacingXs),
          
          // Location row
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: AppTheme.iconXs,
                color: AppTheme.textSecondary,
              ),
              const SizedBox(width: AppTheme.spacingXs),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Location: ',
                        style: AppTheme.labelSmall.copyWith(
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: job.location,
                        style: AppTheme.labelSmall.copyWith(
                          color: AppTheme.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacingXs),
          
          // Hours and Per Diem row
          Row(
            children: [
              // Hours
              if (job.hours != null) ...[
                Icon(
                  Icons.schedule,
                  size: AppTheme.iconXs,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(width: AppTheme.spacingXs),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Hours: ',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: '${job.hours}',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Spacer between hours and per diem
              if (job.hours != null && job.perDiem != null)
                const SizedBox(width: AppTheme.spacingMd),
              
              // Per Diem
              if (job.perDiem != null && job.perDiem!.isNotEmpty) ...[
                Icon(
                  Icons.attach_money,
                  size: AppTheme.iconXs,
                  color: AppTheme.accentCopper,
                ),
                const SizedBox(width: AppTheme.spacingXs),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Per Diem: ',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: job.perDiem!,
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.accentCopper,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: AppTheme.spacingMd),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: JJSecondaryButton(
                  text: 'Details',
                  onPressed: onViewDetails,
                  height: 32,
                ),
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: JJPrimaryButton(
                  text: 'Apply',
                  onPressed: onBidNow,
                  height: 32,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the full-size (detailed) variant of the job card
  Widget _buildFullCard() {
    return JJCard(
      onTap: onTap,
      margin: margin ?? const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMd,
        vertical: AppTheme.spacingSm,
      ),
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row - Local, Classification, and Favorite button
          Row(
            children: [
              // Local
              if (job.local != null)
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: 'Local: ',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(
                          text: '${job.local}',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.primaryNavy,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              
              // Vertical divider
              if (job.local != null && job.classification != null)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMd),
                  width: 1,
                  height: 16,
                  color: AppTheme.lightGray,
                ),
              
              // Classification
              if (job.classification != null)
                Expanded(
                  child: Text(
                    job.classification!,
                    style: AppTheme.titleMedium.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              
              // Favorite button
              if (onFavorite != null)
                GestureDetector(
                  onTap: onFavorite,
                  child: Icon(
                    isFavorited ? Icons.bookmark : Icons.bookmark_border,
                    size: AppTheme.iconMd,
                    color: isFavorited ? AppTheme.accentCopper : AppTheme.textLight,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacingMd),
          
          // Posted time
          if (job.datePosted != null)
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: AppTheme.iconSm,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(width: AppTheme.spacingXs),
                Text(
                  'Posted ${job.datePosted}',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          
          const SizedBox(height: AppTheme.spacingMd),
          
          // Job details grid
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.location_on_outlined,
                  label: 'Location',
                  value: job.location,
                ),
              ),
              if (job.hours != null)
                Expanded(
                  child: _buildDetailItem(
                    icon: Icons.schedule,
                    label: 'Hours',
                    value: '${job.hours}',
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacingMd),
          
          // Wage and Per Diem row
          Row(
            children: [
              if (job.wage != null)
                Expanded(
                  child: _buildDetailItem(
                    icon: Icons.attach_money,
                    label: 'Wages',
                    value: '\$${job.wage!.toStringAsFixed(2)}/hr',
                    isHighlighted: true,
                  ),
                ),
              if (job.perDiem != null && job.perDiem!.isNotEmpty)
                Expanded(
                  child: _buildDetailItem(
                    icon: Icons.hotel,
                    label: 'Per Diem',
                    value: job.perDiem!,
                    isHighlighted: true,
                  ),
                ),
            ],
          ),
          
          // Additional details
          if (job.numberOfJobs != null || job.duration != null) ...[
            const SizedBox(height: AppTheme.spacingMd),
            Row(
              children: [
                if (job.numberOfJobs != null)
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.people,
                      label: 'Positions',
                      value: job.numberOfJobs!,
                    ),
                  ),
                if (job.duration != null)
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.timer,
                      label: 'Duration',
                      value: job.duration!,
                    ),
                  ),
              ],
            ),
          ],
          
          const SizedBox(height: AppTheme.spacingLg),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                flex: 2,
                child: JJSecondaryButton(
                  text: 'Details',
                  onPressed: onViewDetails,
                  icon: Icons.info_outline,
                  height: 44,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                flex: 3,
                child: JJPrimaryButton(
                  text: 'Apply',
                  onPressed: onBidNow,
                  icon: Icons.flash_on,
                  height: 44,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds a detail item with icon, label, and value
  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
    bool isHighlighted = false,
  }) {
    // Skip rendering if value is empty or contains "competitive" 
    if (value.isEmpty || value.toLowerCase().contains('competitive')) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: AppTheme.iconXs,
              color: isHighlighted ? AppTheme.accentCopper : AppTheme.textSecondary,
            ),
            const SizedBox(width: AppTheme.spacingXs),
            Expanded(
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '$label: ',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textSecondary,
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    TextSpan(
                      text: value,
                      style: AppTheme.bodySmall.copyWith(
                        color: isHighlighted ? AppTheme.accentCopper : AppTheme.textPrimary,
                        fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
