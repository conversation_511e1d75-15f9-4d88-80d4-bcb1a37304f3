# Team Review

/spawn --parallel --agent-ui-ux-designer --agent-flutter-expert --agent-frontend-developer  --agent-arcitect-reviewer --ultrathink --mcp-all --uc

--dangerously-skip-permissions = *auto-approves all commands*

*__instructions__*: Spawn this team of very skilled, specialized, and highly experienced agents to read @general-correction.md and implement the corrections to Journeyman Jobs v3 to correct known errors and issues as well as iterate on the new Transformer Bank feature. Once all of the agents have been spawned and initialized. Read @general-corrections.md and create a new plan.md and tasks.md files specifically for this project. hile creating both of the files keep in mind what agents you have and what they specialize in. You need to group the agents together by three's. The agents need to be able to complement each others capabilities. Be sure to, in your plan and tasks.d files to both help with the planning phase and to assign tasks to the agent groups and to coordinate the work of the agents.
<!-->
*mobile-developer*: Ensure cross-platform compatibility:

- Touch gesture handling for drag-and-drop
- Performance optimization for animations
- Offline capability for training mode
- Screen size adaptations for tablets/phones
-->
*ui-ux-designer*: Design the complete user flow and interface specifications for both Reference and Training modes of the Transformer Bank feature. Create wireframes showing:

- Settings screen integration for "Transformer Bank" option
- Reference mode with interactive transformer diagrams
- Training mode with drag-and-drop interfaces
- Difficulty level selection screens
- Animation states for correct/incorrect wiring
- Visual differentiation between difficulty levels (consider color schemes, animations, complexity indicators)

*flutter-expert*: Review the existing Flutter app structure and define the technical architecture for integrating the Transformer Bank feature including:

- Widget hierarchy for both modes
- State management approach (Provider pattern integration)
- Animation controllers for electrical fire/success animations
- Drag-and-drop implementation strategy
- Data models for transformer configurations

*frontend-developer*: Implement the Transformer Bank feature based on the provided specifications, ensuring:

- Compliance with the app's design theme
- Interactive transformer diagrams in Reference mode
- Drag-and-drop functionality in Training mode
- Difficulty level selection and persistence
- Correct/incorrect wiring animations
- Visual differentiation between difficulty levels
<!-->
*search-specialest*: Support the other agents by providing real-time up-to-date information from the internet.

- Provide information on any relevant topics that the other agents may ask about.
- Answer any questions that the other agents may have.
- Provide any additional information that the other agents may need

*performance-engineer*: Optimize the performance of the app, especially the new Transformer Bank feature. This includes:

- Memory management for animations
- Performance optimization for animations
- Performance optimization for drag-and-drop functionality
- Performance optimization for difficulty level selection
- Performance optimization for correct/incorrect wiring animations
- Performance optimization for visual differentiation between difficulty levels

*code-reviewer*: Review the code for the Transformer Bank feature and ensure that it meets the following criteria:

- Compliance with the app's design theme
- Compliance with the app's architecture
- Compliance with the app's performance requirements
- Compliance with the app's security requirements
- Compliance with the app's testing requirements
- Compliance with the app's documentation requirements
-->
*architect-reviewer*: Review the architecture of the app and ensure that it meets the following criteria:

- Compliance with the app's design theme
- Compliance with the app's performance requirements
- Compliance with the app's security requirements
- Compliance with the app's testing requirements
- Compliance with the app's documentation requirements
<!-->
*meta-agent*: Coordinate the work of the other agents and ensure that the Transformer Bank feature is implemented correctly. This includes:

- Ensuring that all agents are working together effectively
- Ensuring that all agents are aware of the progress of the other agents
- Ensuring that all agents are aware of any changes that need to be made
- Ensuring that all agents are aware of any issues that need to be resolved

*context-manager*: Manage the context of the conversation and ensure that all agents have the information they need to do their jobs. This includes:

- Keeping track of the progress of the conversation
- Keeping track of the decisions that have been made
- Keeping track of the issues that have been identified
- Keeping track of the changes that have been made

*dx-optimizer*: Optimize the developer experience for the team. This includes:

- Ensuring that all agents have the information they need to do their jobs
- Ensuring that all agents are aware of the progress of the other agents
- Ensuring that all agents are aware of any changes that need to be made
- Ensuring that all agents are aware of any issues that need to be resolved

*security-auditor*: Review the code for the Transformer Bank feature and ensure that it meets the following criteria:

- Compliance with the app's security requirements
- Compliance with the app's testing requirements
- Compliance with the app's documentation requirements
-->