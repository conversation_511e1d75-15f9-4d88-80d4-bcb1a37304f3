import 'package:flutter/material.dart';
import '../app_theme.dart';
import '../../models/job_model.dart';
import 'reusable_components.dart';
import 'enhanced_backgrounds.dart';

/// Enum for JobCard variants
enum JobCardVariant {
  /// Half-size variant for home screen and compact displays
  half,
  /// Full-size variant for jobs screen with detailed information
  full,
}

/// Enhanced JobCard component with electrical theme
/// Supports two variants: half (compact) and full (detailed)
class JobCard extends StatelessWidget {
  /// The job object containing all job data
  final Job job;
  
  /// The variant type determining the card size and information display
  final JobCardVariant variant;
  
  /// Callback function for when the card is tapped
  final VoidCallback? onTap;
  
  /// Callback function for the "View Details" button
  final VoidCallback? onViewDetails;
  
  /// Callback function for the "Bid Now" button
  final VoidCallback? onBidNow;
  
  /// Callback function for favoriting/bookmarking the job
  final VoidCallback? onFavorite;
  
  /// Whether the job is currently favorited
  final bool isFavorited;
  
  /// Optional margin for the card
  final EdgeInsets? margin;
  
  /// Optional padding for the card content
  final EdgeInsets? padding;

  const JobCard({
    super.key,
    required this.job,
    required this.variant,
    this.onTap,
    this.onViewDetails,
    this.onBidNow,
    this.onFavorite,
    this.isFavorited = false,
    this.margin,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    switch (variant) {
      case JobCardVariant.half:
        return _buildHalfCard();
      case JobCardVariant.full:
        return _buildFullCard();
    }
  }

  /// Builds the half-size (compact) variant of the job card
  Widget _buildHalfCard() {
    return EnhancedBackgrounds.enhancedCardBackground(
      onTap: onTap,
      margin: margin ?? const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSm,
        vertical: AppTheme.spacingXs,
      ),
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingMd),
      showCircuitPattern: false, // Keep subtle for compact cards
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header row - Local and Classification with electrical theme
          Row(
            children: [
              // Enhanced Local indicator
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingSm,
                  vertical: AppTheme.spacingXs,
                ),
                decoration: BoxDecoration(
                  gradient: AppTheme.buttonGradient,
                  borderRadius: BorderRadius.circular(AppTheme.radiusXs),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.electrical_services,
                      size: 12,
                      color: AppTheme.white,
                    ),
                    const SizedBox(width: AppTheme.spacingXs),
                    Text(
                      'Local ${job.local ?? "N/A"}',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              // Storm work indicator
              if (job.isStormWork ?? false)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingXs,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.warningYellow.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusXs),
                    border: Border.all(
                      color: AppTheme.warningYellow,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.thunderstorm,
                        size: 10,
                        color: AppTheme.warningYellow,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        'STORM',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.warningYellow,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSm),

          // Classification with electrical icon
          if (job.classification != null)
            Row(
              children: [
                Icon(
                  _getClassificationIcon(job.classification!),
                  size: 16,
                  color: AppTheme.accentCopper,
                ),
                const SizedBox(width: AppTheme.spacingXs),
                Expanded(
                  child: Text(
                    job.classification!,
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

          // Description
          if (job.description != null) ...[
            const SizedBox(height: AppTheme.spacingXs),
            Text(
              job.description!,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],

          const SizedBox(height: AppTheme.spacingMd),

          // Enhanced action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: onViewDetails,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryNavy,
                    side: BorderSide(
                      color: AppTheme.primaryNavy.withValues(alpha: 0.3),
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingSm,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                    ),
                  ),
                  child: Text(
                    'Details',
                    style: AppTheme.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: ElevatedButton(
                  onPressed: onBidNow,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.accentCopper,
                    foregroundColor: AppTheme.white,
                    elevation: 2,
                    shadowColor: AppTheme.accentCopper.withValues(alpha: 0.3),
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingSm,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.flash_on, size: 14),
                      const SizedBox(width: AppTheme.spacingXs),
                      Text(
                        'Bid',
                        style: AppTheme.bodySmall.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the full-size (detailed) variant of the job card
  Widget _buildFullCard() {
    return EnhancedBackgrounds.enhancedCardBackground(
      onTap: onTap,
      margin: margin ?? const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSm,
        vertical: AppTheme.spacingXs,
      ),
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingMd),
      showCircuitPattern: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with electrical theme
          Row(
            children: [
              // Local indicator with enhanced styling
              Container(
                height: 50,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMd,
                ),
                decoration: BoxDecoration(
                  gradient: AppTheme.buttonGradient,
                  borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                  boxShadow: [AppTheme.shadowSm],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppTheme.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.electrical_services,
                        size: 18,
                        color: AppTheme.white,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingSm),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'IBEW Local',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.white.withValues(alpha: 0.8),
                            fontSize: 10,
                          ),
                        ),
                        Text(
                          '${job.local ?? "N/A"}',
                          style: AppTheme.bodyLarge.copyWith(
                            color: AppTheme.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Spacer(),
              // Favorite button
              Container(
                decoration: BoxDecoration(
                  color: isFavorited ? AppTheme.errorRed : AppTheme.lightGray,
                  shape: BoxShape.circle,
                  boxShadow: [AppTheme.shadowSm],
                ),
                child: IconButton(
                  onPressed: onFavorite,
                  icon: Icon(
                    isFavorited ? Icons.favorite : Icons.favorite_border,
                    color: isFavorited ? AppTheme.white : AppTheme.textSecondary,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),

          // Status indicator for urgent/storm work
          if (job.isStormWork ?? false) ...[
            const SizedBox(height: AppTheme.spacingSm),
            Container(
              decoration: EnhancedBackgrounds.voltageStatusGradient(VoltageLevel.high),
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingMd,
                vertical: AppTheme.spacingSm,
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.thunderstorm,
                    size: 16,
                    color: AppTheme.white,
                  ),
                  const SizedBox(width: AppTheme.spacingSm),
                  Text(
                    'EMERGENCY STORM WORK',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.white,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppTheme.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: AppTheme.spacingMd),

          // Enhanced content section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Classification with electrical icon
              if (job.classification != null)
                Row(
                  children: [
                    Icon(
                      _getClassificationIcon(job.classification!),
                      size: 20,
                      color: AppTheme.accentCopper,
                    ),
                    const SizedBox(width: AppTheme.spacingSm),
                    Expanded(
                      child: Text(
                        job.classification!,
                        style: AppTheme.headlineSmall.copyWith(
                          color: AppTheme.primaryNavy,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              
              // Location
              if (job.location != null) ...[
                const SizedBox(height: AppTheme.spacingSm),
                Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 16,
                      color: AppTheme.textSecondary,
                    ),
                    const SizedBox(width: AppTheme.spacingXs),
                    Expanded(
                      child: Text(
                        job.location!,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              
              // Description
              if (job.description != null) ...[
                const SizedBox(height: AppTheme.spacingMd),
                Text(
                  job.description!,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),

          const SizedBox(height: AppTheme.spacingMd),

          // Electrical details section
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.offWhite,
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              border: Border.all(
                color: AppTheme.accentCopper.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                _buildDetailItem(
                  icon: Icons.schedule,
                  label: 'Duration',
                  value: job.duration ?? 'TBD',
                ),
                const SizedBox(width: AppTheme.spacingLg),
                _buildDetailItem(
                  icon: Icons.attach_money,
                  label: 'Rate',
                  value: job.rate ?? 'Contact Local',
                ),
                const Spacer(),
                // Voltage level indicator
                _buildVoltageIndicator(),
              ],
            ),
          ),

          const SizedBox(height: AppTheme.spacingMd),

          // Enhanced action buttons
          Row(
            children: [
              Expanded(
                flex: 2,
                child: ElevatedButton.icon(
                  onPressed: onBidNow,
                  icon: const Icon(Icons.flash_on, size: 18),
                  label: const Text('Quick Bid'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.accentCopper,
                    foregroundColor: AppTheme.white,
                    elevation: 2,
                    shadowColor: AppTheme.accentCopper.withValues(alpha: 0.3),
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingMd,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: OutlinedButton(
                  onPressed: onViewDetails,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryNavy,
                    side: BorderSide(
                      color: AppTheme.primaryNavy,
                      width: 1.5,
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingMd,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                    ),
                  ),
                  child: const Text('Details'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 14,
              color: AppTheme.accentCopper,
            ),
            const SizedBox(width: AppTheme.spacingXs),
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingXs),
        Text(
          value,
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.primaryNavy,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildVoltageIndicator() {
    final voltage = _getVoltageLevel();
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingSm,
        vertical: AppTheme.spacingXs,
      ),
      decoration: EnhancedBackgrounds.voltageStatusGradient(voltage),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.bolt,
            size: 14,
            color: AppTheme.white,
          ),
          const SizedBox(width: AppTheme.spacingXs),
          Text(
            _getVoltageText(voltage),
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getClassificationIcon(String classification) {
    if (classification.toLowerCase().contains('lineman')) {
      return Icons.power_outlined;
    } else if (classification.toLowerCase().contains('electrician')) {
      return Icons.electrical_services;
    } else if (classification.toLowerCase().contains('wireman')) {
      return Icons.cable;
    } else if (classification.toLowerCase().contains('operator')) {
      return Icons.settings;
    }
    return Icons.construction;
  }

  VoltageLevel _getVoltageLevel() {
    final classification = job.classification?.toLowerCase() ?? '';
    if (classification.contains('transmission') || 
        classification.contains('lineman')) {
      return VoltageLevel.high;
    } else if (classification.contains('distribution') || 
               classification.contains('substation')) {
      return VoltageLevel.medium;
    }
    return VoltageLevel.low;
  }

  String _getVoltageText(VoltageLevel level) {
    switch (level) {
      case VoltageLevel.high:
        return 'HIGH V';
      case VoltageLevel.medium:
        return 'MED V';
      case VoltageLevel.low:
        return 'LOW V';
    }
  }
}
