import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../app_theme.dart';

/// Collection of electrical-themed loading components
class ElectricalLoadingComponents {
  ElectricalLoadingComponents._();

  /// Power grid connection loading indicator
  static Widget powerGridLoader({
    double size = 80.0,
    Color? primaryColor,
    Color? secondaryColor,
    String? message,
  }) {
    return _PowerGridLoader(
      size: size,
      primaryColor: primaryColor ?? AppTheme.accentCopper,
      secondaryColor: secondaryColor ?? AppTheme.primaryNavy,
      message: message,
    );
  }

  /// Electrical sine wave loader
  static Widget sineWaveLoader({
    double width = 200.0,
    double height = 60.0,
    Color? color,
    String? message,
  }) {
    return _SineWaveLoader(
      width: width,
      height: height,
      color: color ?? AppTheme.accentCopper,
      message: message,
    );
  }

  /// Circuit breaker loading animation
  static Widget circuitBreakerLoader({
    double size = 100.0,
    Color? color,
    String? message,
  }) {
    return _CircuitBreakerLoader(
      size: size,
      color: color ?? AppTheme.accentCopper,
      message: message,
    );
  }

  /// Lightning bolt progress indicator
  static Widget lightningProgress({
    double progress = 0.0,
    double width = 200.0,
    double height = 8.0,
    Color? backgroundColor,
    Color? progressColor,
  }) {
    return _LightningProgress(
      progress: progress,
      width: width,
      height: height,
      backgroundColor: backgroundColor ?? AppTheme.lightGray,
      progressColor: progressColor ?? AppTheme.accentCopper,
    );
  }

  /// Transformer loading animation
  static Widget transformerLoader({
    double size = 120.0,
    Color? primaryColor,
    Color? secondaryColor,
    String? message,
  }) {
    return _TransformerLoader(
      size: size,
      primaryColor: primaryColor ?? AppTheme.accentCopper,
      secondaryColor: secondaryColor ?? AppTheme.primaryNavy,
      message: message,
    );
  }
}

/// Power grid loading animation
class _PowerGridLoader extends StatefulWidget {
  final double size;
  final Color primaryColor;
  final Color secondaryColor;
  final String? message;

  const _PowerGridLoader({
    required this.size,
    required this.primaryColor,
    required this.secondaryColor,
    this.message,
  });

  @override
  State<_PowerGridLoader> createState() => _PowerGridLoaderState();
}

class _PowerGridLoaderState extends State<_PowerGridLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return CustomPaint(
                painter: PowerGridPainter(
                  rotation: _rotationAnimation.value,
                  pulse: _pulseAnimation.value,
                  primaryColor: widget.primaryColor,
                  secondaryColor: widget.secondaryColor,
                ),
              );
            },
          ),
        ),
        if (widget.message != null) ...[
          const SizedBox(height: AppTheme.spacingMd),
          Text(
            widget.message!,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// Sine wave loading animation
class _SineWaveLoader extends StatefulWidget {
  final double width;
  final double height;
  final Color color;
  final String? message;

  const _SineWaveLoader({
    required this.width,
    required this.height,
    required this.color,
    this.message,
  });

  @override
  State<_SineWaveLoader> createState() => _SineWaveLoaderState();
}

class _SineWaveLoaderState extends State<_SineWaveLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat();

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.width,
          height: widget.height,
          child: AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: SineWavePainter(
                  animation: _waveAnimation.value,
                  color: widget.color,
                ),
              );
            },
          ),
        ),
        if (widget.message != null) ...[
          const SizedBox(height: AppTheme.spacingMd),
          Text(
            widget.message!,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// Circuit breaker loading animation
class _CircuitBreakerLoader extends StatefulWidget {
  final double size;
  final Color color;
  final String? message;

  const _CircuitBreakerLoader({
    required this.size,
    required this.color,
    this.message,
  });

  @override
  State<_CircuitBreakerLoader> createState() => _CircuitBreakerLoaderState();
}

class _CircuitBreakerLoaderState extends State<_CircuitBreakerLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _switchAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    )..repeat(reverse: true);

    _switchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: AnimatedBuilder(
            animation: _switchAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: CircuitBreakerPainter(
                  switchState: _switchAnimation.value,
                  color: widget.color,
                ),
              );
            },
          ),
        ),
        if (widget.message != null) ...[
          const SizedBox(height: AppTheme.spacingMd),
          Text(
            widget.message!,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// Lightning progress indicator
class _LightningProgress extends StatelessWidget {
  final double progress;
  final double width;
  final double height;
  final Color backgroundColor;
  final Color progressColor;

  const _LightningProgress({
    required this.progress,
    required this.width,
    required this.height,
    required this.backgroundColor,
    required this.progressColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: LightningProgressPainter(
          progress: progress.clamp(0.0, 1.0),
          backgroundColor: backgroundColor,
          progressColor: progressColor,
        ),
      ),
    );
  }
}

/// Transformer loading animation
class _TransformerLoader extends StatefulWidget {
  final double size;
  final Color primaryColor;
  final Color secondaryColor;
  final String? message;

  const _TransformerLoader({
    required this.size,
    required this.primaryColor,
    required this.secondaryColor,
    this.message,
  });

  @override
  State<_TransformerLoader> createState() => _TransformerLoaderState();
}

class _TransformerLoaderState extends State<_TransformerLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: TransformerPainter(
                  pulse: _pulseAnimation.value,
                  primaryColor: widget.primaryColor,
                  secondaryColor: widget.secondaryColor,
                ),
              );
            },
          ),
        ),
        if (widget.message != null) ...[
          const SizedBox(height: AppTheme.spacingMd),
          Text(
            widget.message!,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

// Custom Painters

class PowerGridPainter extends CustomPainter {
  final double rotation;
  final double pulse;
  final Color primaryColor;
  final Color secondaryColor;

  PowerGridPainter({
    required this.rotation,
    required this.pulse,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Outer ring
    final outerPaint = Paint()
      ..color = primaryColor.withValues(alpha: 0.3)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, radius * 0.9, outerPaint);

    // Rotating energy flow
    final energyPaint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < 8; i++) {
      final angle = rotation + (i * math.pi / 4);
      final start = Offset(
        center.dx + math.cos(angle) * radius * 0.6,
        center.dy + math.sin(angle) * radius * 0.6,
      );
      final end = Offset(
        center.dx + math.cos(angle) * radius * 0.85,
        center.dy + math.sin(angle) * radius * 0.85,
      );
      canvas.drawLine(start, end, energyPaint);
    }

    // Center transformer
    final centerPaint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.3 * pulse, centerPaint);

    // Center icon
    final iconPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    // Draw electrical symbol
    final path = Path();
    path.moveTo(center.dx - 10, center.dy - 5);
    path.lineTo(center.dx + 10, center.dy - 5);
    path.lineTo(center.dx - 5, center.dy + 5);
    path.lineTo(center.dx + 15, center.dy + 5);
    canvas.drawPath(path, iconPaint);
  }

  @override
  bool shouldRepaint(PowerGridPainter oldDelegate) {
    return oldDelegate.rotation != rotation || oldDelegate.pulse != pulse;
  }
}

class SineWavePainter extends CustomPainter {
  final double animation;
  final Color color;

  SineWavePainter({
    required this.animation,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    final amplitude = size.height / 4;
    final frequency = 2;
    final centerY = size.height / 2;

    for (double x = 0; x <= size.width; x += 1) {
      final y = centerY + amplitude * math.sin((x / size.width * frequency * 2 * math.pi) + animation);
      if (x == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);

    // Add glow effect
    final glowPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..strokeWidth = 8.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawPath(path, glowPaint);
  }

  @override
  bool shouldRepaint(SineWavePainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

class CircuitBreakerPainter extends CustomPainter {
  final double switchState;
  final Color color;

  CircuitBreakerPainter({
    required this.switchState,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()
      ..color = color
      ..strokeWidth = 4.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Circuit breaker housing
    final housingRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: center,
        width: size.width * 0.8,
        height: size.height * 0.6,
      ),
      const Radius.circular(8),
    );
    canvas.drawRRect(housingRect, paint);

    // Switch lever
    final leverPaint = Paint()
      ..color = switchState > 0.5 ? Colors.green : Colors.red
      ..strokeWidth = 6.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final leverAngle = math.pi / 6 * (switchState - 0.5) * 2;
    final leverLength = size.width * 0.25;
    final leverEnd = Offset(
      center.dx + math.cos(leverAngle) * leverLength,
      center.dy + math.sin(leverAngle) * leverLength,
    );

    canvas.drawLine(center, leverEnd, leverPaint);

    // Connection points
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(center.dx - size.width * 0.3, center.dy),
      6,
      pointPaint,
    );
    canvas.drawCircle(
      Offset(center.dx + size.width * 0.3, center.dy),
      6,
      pointPaint,
    );
  }

  @override
  bool shouldRepaint(CircuitBreakerPainter oldDelegate) {
    return oldDelegate.switchState != switchState;
  }
}

class LightningProgressPainter extends CustomPainter {
  final double progress;
  final Color backgroundColor;
  final Color progressColor;

  LightningProgressPainter({
    required this.progress,
    required this.backgroundColor,
    required this.progressColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Background
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final backgroundPath = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(size.height / 2),
      ));

    canvas.drawPath(backgroundPath, backgroundPaint);

    // Progress with lightning bolt pattern
    if (progress > 0) {
      final progressPaint = Paint()
        ..color = progressColor
        ..style = PaintingStyle.fill;

      final progressWidth = size.width * progress;
      final lightningPath = Path();

      // Create lightning bolt pattern
      lightningPath.moveTo(0, 0);
      lightningPath.lineTo(progressWidth * 0.3, 0);
      lightningPath.lineTo(progressWidth * 0.2, size.height * 0.5);
      lightningPath.lineTo(progressWidth * 0.6, size.height * 0.5);
      lightningPath.lineTo(progressWidth * 0.5, size.height);
      lightningPath.lineTo(progressWidth, size.height);
      lightningPath.lineTo(progressWidth, 0);

      canvas.clipPath(backgroundPath);
      canvas.drawPath(lightningPath, progressPaint);
    }
  }

  @override
  bool shouldRepaint(LightningProgressPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

class TransformerPainter extends CustomPainter {
  final double pulse;
  final Color primaryColor;
  final Color secondaryColor;

  TransformerPainter({
    required this.pulse,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    // Primary coil
    paint.color = primaryColor;
    for (int i = 0; i < 5; i++) {
      final radius = (size.width * 0.2) + (i * 8) + (pulse * 5);
      canvas.drawCircle(
        Offset(center.dx - size.width * 0.15, center.dy),
        radius,
        paint,
      );
    }

    // Secondary coil
    paint.color = secondaryColor;
    for (int i = 0; i < 5; i++) {
      final radius = (size.width * 0.2) + (i * 8) + ((1 - pulse) * 5);
      canvas.drawCircle(
        Offset(center.dx + size.width * 0.15, center.dy),
        radius,
        paint,
      );
    }

    // Core
    final corePaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 6.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(center.dx - size.width * 0.35, center.dy - size.height * 0.3),
      Offset(center.dx - size.width * 0.35, center.dy + size.height * 0.3),
      corePaint,
    );
    canvas.drawLine(
      Offset(center.dx + size.width * 0.35, center.dy - size.height * 0.3),
      Offset(center.dx + size.width * 0.35, center.dy + size.height * 0.3),
      corePaint,
    );
  }

  @override
  bool shouldRepaint(TransformerPainter oldDelegate) {
    return oldDelegate.pulse != pulse;
  }
}
