{"permissions": {"allow": ["WebFetch(domain:github.com)", "Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(npm install:*)", "<PERSON><PERSON>(git clone:*)", "Bash(cp:*)", "mcp__serena__find_file", "mcp__serena__list_dir", "mcp__serena__activate_project", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__find_symbol", "mcp__serena__get_symbols_overview", "mcp__serena__think_about_collected_information", "mcp__serena__write_memory", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__read_memory", "mcp__serena__search_for_pattern", "mcp__serena__list_memories", "Bash(flutter analyze:*)", "Bash(grep:*)", "multiEdit", "webFetch", "task", "todo", "webSearch", "Bash(rg:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(cd:*)", "Ba<PERSON>(flutter:*)", "<PERSON><PERSON>(sed:*)", "Bash(/mnt/c/flutter/bin/flutter pub get)", "Bash(/mnt/c/flutter/flutter/bin/flutter pub get)", "<PERSON><PERSON>(chmod:*)", "Bash(for file in lib/services/location_service.dart lib/providers/app_state_provider.dart lib/providers/auth_provider.dart)", "Bash(do)", "Bash(done)", "Bash(./scripts/team-health.sh:*)", "<PERSON><PERSON>(make:*)", "Bash(dart analyze:*)", "WebFetch(domain:firebase.google.com)", "WebFetch(domain:)", "Bash(/mnt/c/flutter/bin/flutter:*)", "Bash(/mnt/c/flutter/flutter/bin/flutter analyze --no-pub)", "<PERSON><PERSON>(test:*)"], "deny": [], "additionalDirectories": ["/tmp"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["Bright Data", "mcp-server-fetch", "sequential-thinking"], "disableAllHooks": true}