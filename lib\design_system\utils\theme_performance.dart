import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/material.dart';

/// Performance monitoring and optimization utilities for theme elements
class ThemePerformanceMonitor {
  static bool _isEnabled = kDebugMode;
  static final Map<String, int> _repaintCounts = {};
  static final Map<String, Duration> _paintDurations = {};

  /// Enable or disable performance monitoring
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// Track repaint operations for custom painters
  static void trackRepaint(String painterId) {
    if (!_isEnabled) return;
    
    _repaintCounts[painterId] = (_repaintCounts[painterId] ?? 0) + 1;
    
    if (_repaintCounts[painterId]! % 10 == 0) {
      debugPrint('ThemePerformance: $painterId has repainted ${_repaintCounts[painterId]} times');
    }
  }

  /// Track paint duration for performance analysis
  static void trackPaintDuration(String painterId, Duration duration) {
    if (!_isEnabled) return;
    
    _paintDurations[painterId] = duration;
    
    if (duration.inMilliseconds > 16) { // More than one frame at 60fps
      debugPrint('ThemePerformance: $painterId took ${duration.inMilliseconds}ms to paint (>16ms warning)');
    }
  }

  /// Get performance statistics
  static Map<String, dynamic> getStats() {
    return {
      'repaintCounts': Map.from(_repaintCounts),
      'paintDurations': _paintDurations.map(
        (key, value) => MapEntry(key, value.inMilliseconds),
      ),
    };
  }

  /// Clear all statistics
  static void clearStats() {
    _repaintCounts.clear();
    _paintDurations.clear();
  }
}

/// Performance-optimized CustomPainter base class
abstract class PerformantCustomPainter extends CustomPainter {
  final String painterId;
  
  const PerformantCustomPainter(this.painterId);

  @override
  void paint(Canvas canvas, Size size) {
    final stopwatch = Stopwatch()..start();
    
    paintImplementation(canvas, size);
    
    stopwatch.stop();
    ThemePerformanceMonitor.trackPaintDuration(painterId, stopwatch.elapsed);
    ThemePerformanceMonitor.trackRepaint(painterId);
  }

  /// Implement the actual painting logic here
  void paintImplementation(Canvas canvas, Size size);
}

/// Gradient cache for performance optimization
class GradientCache {
  static final Map<String, Shader> _shaderCache = {};
  static final Map<String, Paint> _paintCache = {};

  /// Get cached shader or create new one
  static Shader getShader(String key, Gradient gradient, Rect rect) {
    final cacheKey = '$key-${rect.width}x${rect.height}';
    
    if (!_shaderCache.containsKey(cacheKey)) {
      _shaderCache[cacheKey] = gradient.createShader(rect);
    }
    
    return _shaderCache[cacheKey]!;
  }

  /// Get cached paint with gradient
  static Paint getPaint(String key, Gradient gradient, Rect rect) {
    final cacheKey = '$key-${rect.width}x${rect.height}';
    
    if (!_paintCache.containsKey(cacheKey)) {
      _paintCache[cacheKey] = Paint()
        ..shader = gradient.createShader(rect);
    }
    
    return _paintCache[cacheKey]!;
  }

  /// Clear cache when memory pressure is high
  static void clearCache() {
    _shaderCache.clear();
    _paintCache.clear();
  }

  /// Get cache statistics
  static Map<String, int> getCacheStats() {
    return {
      'shaderCacheSize': _shaderCache.length,
      'paintCacheSize': _paintCache.length,
    };
  }
}

/// Widget performance wrapper with automatic optimization
class PerformanceOptimizedWidget extends StatelessWidget {
  final Widget child;
  final bool useRepaintBoundary;
  final String? debugLabel;

  const PerformanceOptimizedWidget({
    super.key,
    required this.child,
    this.useRepaintBoundary = true,
    this.debugLabel,
  });

  @override
  Widget build(BuildContext context) {
    Widget optimizedChild = child;

    // Add RepaintBoundary for complex widgets
    if (useRepaintBoundary) {
      optimizedChild = RepaintBoundary(child: optimizedChild);
    }

    // Add debug information in development
    if (kDebugMode && debugLabel != null) {
      optimizedChild = Builder(
        builder: (context) {
          debugPrint('Rendering optimized widget: $debugLabel');
          return optimizedChild;
        },
      );
    }

    return optimizedChild;
  }
}

/// Animation performance utilities
class AnimationPerformanceUtils {
  /// Check if device can handle complex animations
  static bool get canHandleComplexAnimations {
    // This is a simplified check - in production you might want to check
    // device capabilities, frame rate, etc.
    return !kIsWeb; // Web typically has different performance characteristics
  }

  /// Get recommended animation duration based on complexity
  static Duration getRecommendedDuration(AnimationComplexity complexity) {
    if (!canHandleComplexAnimations) {
      // Reduce animation duration on less capable devices
      switch (complexity) {
        case AnimationComplexity.simple:
          return const Duration(milliseconds: 150);
        case AnimationComplexity.medium:
          return const Duration(milliseconds: 200);
        case AnimationComplexity.complex:
          return const Duration(milliseconds: 250);
      }
    }

    switch (complexity) {
      case AnimationComplexity.simple:
        return const Duration(milliseconds: 200);
      case AnimationComplexity.medium:
        return const Duration(milliseconds: 300);
      case AnimationComplexity.complex:
        return const Duration(milliseconds: 500);
    }
  }

  /// Create performance-aware animation curve
  static Curve getPerformanceCurve({bool reducedMotion = false}) {
    if (reducedMotion || !canHandleComplexAnimations) {
      return Curves.linear;
    }
    return Curves.easeInOutCubic;
  }
}

enum AnimationComplexity { simple, medium, complex }

/// Theme-specific performance recommendations
class ThemePerformanceRecommendations {
  /// Check if gradient should be simplified for performance
  static bool shouldSimplifyGradient(Gradient gradient, Size size) {
    // Simplify gradients on large surfaces or complex gradients
    if (size.width * size.height > 1000000) { // Large surface area
      return true;
    }
    
    if (gradient is LinearGradient && gradient.colors.length > 3) {
      return true; // Complex gradient
    }
    
    if (gradient is RadialGradient && gradient.colors.length > 2) {
      return true; // Complex radial gradient
    }
    
    return false;
  }

  /// Get simplified version of gradient for performance
  static Gradient simplifyGradient(Gradient gradient) {
    if (gradient is LinearGradient) {
      return LinearGradient(
        begin: gradient.begin,
        end: gradient.end,
        colors: [
          gradient.colors.first,
          gradient.colors.last,
        ],
      );
    }
    
    if (gradient is RadialGradient) {
      return RadialGradient(
        center: gradient.center,
        radius: gradient.radius,
        colors: [
          gradient.colors.first,
          gradient.colors.last,
        ],
      );
    }
    
    return gradient;
  }

  /// Recommend whether to use CustomPainter or Container for background
  static bool shouldUseCustomPainter(Size size, {bool hasAnimation = false}) {
    // Use CustomPainter for large areas or when animation is involved
    return hasAnimation || (size.width * size.height > 500000);
  }
}

/// Memory-aware theme component
mixin ThemeMemoryAwareness {
  static int _activeGradients = 0;
  static const int _maxActiveGradients = 10;

  void registerGradientUse() {
    _activeGradients++;
    if (_activeGradients > _maxActiveGradients) {
      GradientCache.clearCache();
      _activeGradients = 0;
      if (kDebugMode) {
        debugPrint('ThemeMemoryAwareness: Cleared gradient cache due to memory pressure');
      }
    }
  }

  void unregisterGradientUse() {
    if (_activeGradients > 0) {
      _activeGradients--;
    }
  }
}